#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script para corrigir a exibição de sinais na interface
Remove dados antigos e força atualização real
"""

import sys
import os
import json
from pathlib import Path
from datetime import datetime

# Adiciona o diretório do projeto ao path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

print("🧹 Correção: Sinais na Interface")
print("=" * 50)

try:
    from utils.config import ConfigManager
    from core.mt5_connector import MT5Connector
    from core.analyzer import PairAnalyzer
    from core.trader import AutoTrader
    
    # 1. LIMPA ARQUIVOS DE CACHE ANTIGOS
    print("🧹 Limpando arquivos de cache...")
    
    cache_files = [
        "data/analysis_cache.json",
        "data/signals_cache.json", 
        "data/analyzer_state.json",
        "data/pairs_analysis.json"
    ]
    
    for cache_file in cache_files:
        if os.path.exists(cache_file):
            os.remove(cache_file)
            print(f"   ✅ Removido: {cache_file}")
        else:
            print(f"   ⚪ Não existe: {cache_file}")
    
    # 2. INICIALIZA COMPONENTES LIMPOS
    print("\n🔧 Inicializando componentes...")
    
    config = ConfigManager()
    mt5 = MT5Connector(config)
    
    # Conecta MT5
    if not mt5.check_connection():
        if not mt5.connect():
            print("❌ Falha ao conectar MT5")
            sys.exit(1)
    print("✅ MT5 conectado")
    
    # 3. CRIA ANALYZER LIMPO
    print("\n🔬 Criando analyzer limpo...")
    analyzer = PairAnalyzer(config, mt5)
    
    # Verifica estado inicial
    initial_signals = analyzer.get_active_signals()
    print(f"   Sinais iniciais: {len(initial_signals)}")
    
    initial_analyses = analyzer.get_all_analyses()
    print(f"   Análises iniciais: {len(initial_analyses)}")
    
    # 4. LIMPA CACHES EM MEMÓRIA
    print("\n🧹 Limpando caches em memória...")
    analyzer.signals_cache.clear()
    analyzer.analysis_cache.clear()
    print("✅ Caches limpos")
    
    # 5. FORÇA NOVA ANÁLISE
    print("\n🔄 Forçando nova análise...")
    analyzer.force_full_analysis()
    
    # Aguarda processamento
    import time
    time.sleep(2)
    
    # 6. VERIFICA ESTADO ATUAL
    print("\n📊 Estado após limpeza:")
    
    current_signals = analyzer.get_active_signals()
    print(f"   Sinais ativos: {len(current_signals)}")
    
    current_analyses = analyzer.get_all_analyses()
    print(f"   Análises ativas: {len(current_analyses)}")
    
    # Lista sinais se houver
    if current_signals:
        print("\n📈 SINAIS ATUAIS:")
        for i, signal in enumerate(current_signals, 1):
            age_seconds = (datetime.now() - signal.timestamp).total_seconds()
            print(f"   {i}. {signal.pair_name} - {signal.signal_type}")
            print(f"      Score: {signal.score:.1f}, Z-Score: {signal.z_score:.3f}")
            print(f"      Idade: {age_seconds:.0f}s, Expira: {signal.expires_at.strftime('%H:%M:%S')}")
    
    # Lista análises se houver
    if current_analyses:
        print("\n📊 ANÁLISES ATUAIS:")
        for pair_name, analysis in list(current_analyses.items())[:5]:  # Mostra apenas 5
            print(f"   • {pair_name}: {analysis.status} (Score: {analysis.score:.1f})")
    
    # 7. TESTA AUTOTRADER SYNC
    print("\n🤖 Verificando AutoTrader...")
    try:
        auto_trader = AutoTrader(config, mt5, analyzer)
        trades = auto_trader.active_trades
        print(f"   Trades ativos: {len(trades)}")
        
        # Verifica sincronização
        for trade_id, trade in trades.items():
            has_signal = any(s.pair_name == trade.pair_name for s in current_signals)
            print(f"   • {trade.pair_name}: Trade ativo, Sinal ativo: {'Sim' if has_signal else 'Não'}")
            
    except Exception as e:
        print(f"❌ Erro no AutoTrader: {e}")
    
    # 8. CRIA ARQUIVO DE STATUS PARA INTERFACE
    print("\n💾 Criando arquivo de status...")
    
    status_data = {
        'timestamp': datetime.now().isoformat(),
        'signals_count': len(current_signals),
        'analyses_count': len(current_analyses),
        'cache_cleared': True,
        'signals': [
            {
                'pair_name': s.pair_name,
                'signal_type': s.signal_type,
                'score': s.score,
                'z_score': s.z_score,
                'timestamp': s.timestamp.isoformat(),
                'expires_at': s.expires_at.isoformat()
            }
            for s in current_signals
        ]
    }
    
    # Salva status
    os.makedirs('data', exist_ok=True)
    with open('data/interface_status.json', 'w') as f:
        json.dump(status_data, f, indent=2, default=str)
    
    print("✅ Arquivo de status criado: data/interface_status.json")
    
    # 9. RELATÓRIO FINAL
    print("\n" + "=" * 50)
    print("📋 RELATÓRIO FINAL:")
    print(f"• Caches limpos: ✅")
    print(f"• Análises atualizadas: ✅")
    print(f"• Sinais ativos: {len(current_signals)}")
    print(f"• Status salvo: ✅")
    
    if len(current_signals) == 0:
        print("\n✅ PROBLEMA CORRIGIDO:")
        print("   A interface deveria mostrar 0 sinais ativos")
        print("   Se ainda mostra sinais antigos, é problema de cache da GUI")
    else:
        print(f"\n⚠️ AINDA HÁ {len(current_signals)} SINAIS ATIVOS:")
        print("   Estes são sinais válidos gerados agora")
        print("   A interface deve mostrar exatamente estes sinais")
    
    print("\n📌 PRÓXIMOS PASSOS:")
    print("1. Restart a interface gráfica")
    print("2. Verifique se mostra apenas os sinais listados acima")
    print("3. Se ainda mostra dados antigos, há cache na GUI")
    
except Exception as e:
    print(f"❌ Erro geral: {e}")
    import traceback
    traceback.print_exc() 