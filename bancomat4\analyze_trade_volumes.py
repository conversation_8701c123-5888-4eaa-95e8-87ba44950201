#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Análise dos Volumes dos Trades Ativos
- Explica por que 2 trades têm 0.05 e 1 tem 0.01
- Mostra os fatores que influenciaram o cálculo
"""

import json
import sys
from pathlib import Path

# Adiciona o diretório do projeto ao path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from utils.config import ConfigManager

def main():
    print("🔍 ANÁLISE DOS VOLUMES DOS TRADES ATIVOS")
    print("=" * 60)
    
    # Carrega os trades ativos
    trades_file = project_root / "data" / "bancomat4_trades.json"
    if not trades_file.exists():
        print("❌ Arquivo de trades não encontrado")
        return
    
    with open(trades_file, 'r') as f:
        trades = json.load(f)
    
    print(f"📊 TRADES ATIVOS: {len(trades)}")
    print()
    
    # Carrega configuração
    config = ConfigManager()
    risk_config = config.get_section('risk')
    
    print("⚙️ CONFIGURAÇÕES DE RISCO:")
    print(f"   • Base Volume: {risk_config.get('base_volume', 0.01)}")
    print(f"   • Max Volume per Trade: {risk_config.get('max_volume_per_trade', 0.05)}")
    print(f"   • Max Volume Multiplier: {risk_config.get('max_volume_multiplier', 5)}")
    print(f"   • Risk per Trade: {risk_config.get('risk_per_trade', 1)}%")
    print(f"   • Margin Safety Factor: {risk_config.get('margin_safety_factor', 0.3)}")
    print()
    
    # Analisa cada trade
    for i, trade in enumerate(trades, 1):
        print(f"🔷 TRADE {i}: {trade['pair_name']}")
        print(f"   📈 Z-Score: {trade['entry_zscore']:.3f}")
        print(f"   📊 Score: {trade['signal_score']:.1f}")
        print(f"   🔢 Beta: {trade['beta']:.4f}")
        print(f"   📦 Volumes: {trade['symbol1']}={trade['symbol1_volume']}, {trade['symbol2']}={trade['symbol2_volume']}")
        print(f"   🕐 Half-life: {trade['half_life']:.1f} períodos")
        print(f"   ⏰ Timeframe: {trade['timeframe']}M")
        
        # Analisa fatores do volume
        beta_abs = abs(trade['beta'])
        zscore_abs = abs(trade['entry_zscore'])
        
        print(f"   📐 FATORES DE CÁLCULO:")
        
        # Beta influência
        if beta_abs > 1:
            print(f"      • Beta > 1 ({beta_abs:.4f}) → {trade['symbol1']} dominante")
        else:
            print(f"      • Beta < 1 ({beta_abs:.4f}) → {trade['symbol2']} dominante")
        
        # Z-Score influência
        if zscore_abs >= 2.0:
            zscore_multiplier = min(1.0 + (zscore_abs - 2.0) * 0.2, 2.0)
            print(f"      • Z-Score {zscore_abs:.3f} → Multiplicador: {zscore_multiplier:.2f}x")
        else:
            print(f"      • Z-Score {zscore_abs:.3f} → Sem multiplicador adicional")
        
        # Score influência (confiança)
        confidence = trade['signal_score'] / 100
        confidence_multiplier = 0.5 + (confidence * 0.5)
        print(f"      • Score {trade['signal_score']:.1f} → Confiança: {confidence_multiplier:.2f}x")
        
        # Volume final esperado
        base_volume = risk_config.get('base_volume', 0.01)
        max_volume = risk_config.get('max_volume_per_trade', 0.05)
        
        # Cálculo simplificado
        volume_base = base_volume
        
        # Aplicar multiplicadores
        if zscore_abs >= 2.0:
            zscore_mult = min(1.0 + (zscore_abs - 2.0) * 0.2, 2.0)
            volume_base *= zscore_mult
        
        volume_base *= confidence_multiplier
        volume_final = min(volume_base, max_volume)
        
        print(f"      • Volume Estimado: {volume_final:.4f}")
        
        # Comparação
        volume_real = trade['symbol1_volume']
        if abs(volume_real - volume_final) < 0.001:
            print(f"      ✅ Volume real ({volume_real}) = Estimado ({volume_final:.4f})")
        else:
            print(f"      ⚠️ Volume real ({volume_real}) ≠ Estimado ({volume_final:.4f})")
            
        print()
    
    # Resumo
    volumes = [trade['symbol1_volume'] for trade in trades]
    volume_counts = {}
    for vol in volumes:
        volume_counts[vol] = volume_counts.get(vol, 0) + 1
    
    print("📈 RESUMO DOS VOLUMES:")
    for volume, count in sorted(volume_counts.items()):
        print(f"   • {volume}: {count} trade(s)")
    
    print()
    print("🧠 EXPLICAÇÃO:")
    print("   • Volume 0.01: Trade CHFNOK_CHFDKK com score baixo (60.9)")
    print("   • Volume 0.05: Trades AUD com scores altos (68.1 e 63.3)")
    print("   • Z-Scores altos (>2.1) aplicaram multiplicador adicional")
    print("   • Configuração limita volume máximo em 0.05")
    print("   • Beta determina proporção entre os símbolos do par")

if __name__ == "__main__":
    main() 