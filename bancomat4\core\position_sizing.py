#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Sistema de Position Sizing para Bancomat 4
- Cálculo de volume proporcional baseado em beta
- Gestão de risco dinâmica
- Validação de margem
"""

import math
from typing import Dict, Tuple, Optional, Any
from utils.logger import get_logger
from utils.config import ConfigManager
from core.mt5_connector import MT5Connector

logger = get_logger(__name__)


class PositionSizer:
    """Sistema de dimensionamento de posições"""
    
    def __init__(self, config: ConfigManager, mt5: MT5Connector):
        self.config = config
        self.mt5 = mt5
        self.risk_config = config.get_section('risk')
        
        logger.info("💰 Sistema de position sizing inicializado")
    
    def calculate_volumes(self, symbol1: str, symbol2: str, beta: float, 
                         z_score: float = 0.0, confidence: float = 1.0) -> Dict[str, Any]:
        """
        Calcula volumes para um par baseado em beta e gestão de risco
        
        Args:
            symbol1: Primeiro símbolo
            symbol2: Segundo símbolo  
            beta: Beta (hedge ratio) da relação
            z_score: Z-Score atual (para ajuste de volume)
            confidence: Confiança no sinal (0-1)
            
        Returns:
            Dict com volumes calculados e informações de margem
        """
        try:
            logger.info(f"💱 Calculando volumes para {symbol1}/{symbol2}, beta: {beta:.4f}")
            
            # Obtém informações da conta
            account_info = self.mt5.get_account_info()
            if not account_info:
                return self._get_minimum_volumes(symbol1, symbol2, "Erro ao obter informações da conta")
            
            # Obtém informações dos símbolos
            symbol1_info = self.mt5.get_symbol_info(symbol1)
            symbol2_info = self.mt5.get_symbol_info(symbol2)
            
            if not symbol1_info or not symbol2_info:
                return self._get_minimum_volumes(symbol1, symbol2, "Erro ao obter informações dos símbolos")
            
            # Calcula volumes baseado no modo de position sizing
            position_sizing = self.risk_config.get('position_sizing', 'proportional')
            
            if position_sizing == 'proportional':
                return self._calculate_proportional_volumes(
                    symbol1, symbol2, symbol1_info, symbol2_info, 
                    account_info, beta, z_score, confidence
                )
            else:
                return self._calculate_minimum_volumes(symbol1, symbol2, symbol1_info, symbol2_info)
                
        except Exception as e:
            logger.error(f"Erro ao calcular volumes: {e}")
            return self._get_minimum_volumes(symbol1, symbol2, f"Exceção: {str(e)}")
    
    def _calculate_proportional_volumes(self, symbol1: str, symbol2: str, 
                                      symbol1_info: Dict, symbol2_info: Dict,
                                      account_info, beta: float, 
                                      z_score: float, confidence: float) -> Dict[str, Any]:
        """Calcula volumes proporcionais baseados em beta e risco"""
        try:
            # Parâmetros de risco - account_info é objeto AccountInfo, não dict
            balance = account_info.balance
            margin_free = account_info.margin_free
            risk_per_trade = self.risk_config.get('risk_per_trade', 1.0) / 100  # Converte %
            margin_safety_factor = self.risk_config.get('margin_safety_factor', 0.3)
            base_volume = self.risk_config.get('base_volume', 0.01)
            max_volume_multiplier = self.risk_config.get('max_volume_multiplier', 10)
            
            # Valor em risco
            risk_amount = balance * risk_per_trade
            
            # Margem disponível para este trade
            available_margin = margin_free * margin_safety_factor
            
            # Informações dos símbolos
            symbol1_min_lot = symbol1_info.get('volume_min', 0.01)
            symbol2_min_lot = symbol2_info.get('volume_min', 0.01)
            symbol1_lot_step = symbol1_info.get('volume_step', 0.01)
            symbol2_lot_step = symbol2_info.get('volume_step', 0.01)
            
            # Obtém preços atuais
            symbol1_tick = self.mt5.get_tick_info(symbol1)
            symbol2_tick = self.mt5.get_tick_info(symbol2)
            
            if not symbol1_tick or not symbol2_tick:
                logger.warning("Não foi possível obter preços atuais")
                return self._calculate_minimum_volumes(symbol1, symbol2, symbol1_info, symbol2_info)
            
            symbol1_price = symbol1_tick.get('ask', 1.0)
            symbol2_price = symbol2_tick.get('ask', 1.0)
            
            # Calcula margem necessária para 1 lote de cada símbolo
            try:
                import MetaTrader5 as mt5
                
                # Calcula margem para lotes unitários
                margin1_per_lot = mt5.order_calc_margin(mt5.ORDER_TYPE_BUY, symbol1, 1.0, symbol1_price) or 1000
                margin2_per_lot = mt5.order_calc_margin(mt5.ORDER_TYPE_SELL, symbol2, 1.0, symbol2_price) or 1000
                
            except Exception as e:
                logger.warning(f"Erro ao calcular margem via MT5: {e}. Usando estimativa.")
                # Estimativa conservadora: 1 lote = 1/100 do valor do contrato
                # account_info também é objeto, não dict
                leverage = getattr(account_info, 'leverage', 100)
                contract_size = 100000  # Padrão forex
                
                margin1_per_lot = (contract_size * symbol1_price) / leverage
                margin2_per_lot = (contract_size * symbol2_price) / leverage
            
            # Calcula volumes base considerando o beta
            beta_abs = abs(beta) if beta != 0 else 1.0
            
            # Volume inicial baseado na margem disponível
            # Distribui a margem considerando a proporção do beta
            if beta_abs > 1:
                # Symbol1 dominante
                margin_ratio = beta_abs / (1 + beta_abs)
                symbol1_margin = available_margin * margin_ratio
                symbol2_margin = available_margin * (1 - margin_ratio)
            else:
                # Symbol2 dominante
                margin_ratio = 1 / (1 + beta_abs)
                symbol1_margin = available_margin * margin_ratio
                symbol2_margin = available_margin * (1 - margin_ratio)
            
            # Calcula volumes baseados na margem
            symbol1_volume_from_margin = symbol1_margin / margin1_per_lot
            symbol2_volume_from_margin = symbol2_margin / margin2_per_lot
            
            # Ajusta volume de symbol1 para manter proporção do beta
            symbol1_volume = max(base_volume, symbol1_volume_from_margin)
            symbol2_volume = max(base_volume, symbol1_volume * beta_abs)
            
            # Ajuste baseado na confiança do sinal
            confidence_multiplier = 0.5 + (confidence * 0.5)  # 0.5x a 1.0x
            symbol1_volume *= confidence_multiplier
            symbol2_volume *= confidence_multiplier
            
            # Ajuste baseado na intensidade do Z-Score
            zscore_abs = abs(z_score)
            if zscore_abs >= 2.0:
                # Quanto maior o Z-Score, maior o volume (até um limite)
                zscore_multiplier = min(1.0 + (zscore_abs - 2.0) * 0.2, 2.0)  # Máximo 2x
                symbol1_volume *= zscore_multiplier
                symbol2_volume *= zscore_multiplier
            
            # Limita volumes máximos
            max_volume1 = base_volume * max_volume_multiplier
            max_volume2 = base_volume * max_volume_multiplier
            
            symbol1_volume = min(symbol1_volume, max_volume1)
            symbol2_volume = min(symbol2_volume, max_volume2)
            
            # Ajusta para steps dos símbolos
            symbol1_volume = max(symbol1_min_lot, 
                               round(symbol1_volume / symbol1_lot_step) * symbol1_lot_step)
            symbol2_volume = max(symbol2_min_lot,
                               round(symbol2_volume / symbol2_lot_step) * symbol2_lot_step)
            
            # Calcula margem total estimada
            total_margin_required = (symbol1_volume * margin1_per_lot + 
                                   symbol2_volume * margin2_per_lot)
            
            # Verifica se tem margem suficiente
            has_sufficient_margin = total_margin_required <= available_margin
            
            if not has_sufficient_margin:
                # Reduz volumes proporcionalmente
                reduction_factor = available_margin / total_margin_required * 0.9  # Margem de segurança
                
                symbol1_volume *= reduction_factor
                symbol2_volume *= reduction_factor
                
                # Reajusta para steps
                symbol1_volume = max(symbol1_min_lot,
                                   round(symbol1_volume / symbol1_lot_step) * symbol1_lot_step)
                symbol2_volume = max(symbol2_min_lot,
                                   round(symbol2_volume / symbol2_lot_step) * symbol2_lot_step)
                
                # Recalcula margem
                total_margin_required = (symbol1_volume * margin1_per_lot + 
                                       symbol2_volume * margin2_per_lot)
                
                logger.warning(f"Volumes reduzidos por margem insuficiente: {symbol1}={symbol1_volume:.4f}, {symbol2}={symbol2_volume:.4f}")
            
            result = {
                'symbol1': symbol1,
                'symbol2': symbol2,
                'symbol1_volume': symbol1_volume,
                'symbol2_volume': symbol2_volume,
                'has_sufficient_margin': total_margin_required <= available_margin,
                'total_margin_required': total_margin_required,
                'margin_available': available_margin,
                'margin_free': margin_free,
                'beta': beta,
                'beta_abs': beta_abs,
                'risk_amount': risk_amount,
                'confidence_multiplier': confidence_multiplier,
                'zscore_multiplier': zscore_multiplier if 'zscore_multiplier' in locals() else 1.0,
                'margin1_per_lot': margin1_per_lot,
                'margin2_per_lot': margin2_per_lot,
                'method': 'proportional',
                'success': True
            }
            
            logger.info(f"✅ Volumes calculados - {symbol1}: {symbol1_volume:.4f}, {symbol2}: {symbol2_volume:.4f}")
            logger.info(f"💰 Margem necessária: {total_margin_required:.2f}, Disponível: {available_margin:.2f}")
            
            return result
            
        except Exception as e:
            logger.error(f"Erro no cálculo proporcional: {e}")
            return self._calculate_minimum_volumes(symbol1, symbol2, symbol1_info, symbol2_info)
    
    def _calculate_minimum_volumes(self, symbol1: str, symbol2: str, 
                                 symbol1_info: Dict, symbol2_info: Dict) -> Dict[str, Any]:
        """Calcula volumes mínimos"""
        try:
            symbol1_volume = symbol1_info.get('volume_min', 0.01)
            symbol2_volume = symbol2_info.get('volume_min', 0.01)
            
            logger.info(f"📉 Usando volumes mínimos - {symbol1}: {symbol1_volume}, {symbol2}: {symbol2_volume}")
            
            return {
                'symbol1': symbol1,
                'symbol2': symbol2,
                'symbol1_volume': symbol1_volume,
                'symbol2_volume': symbol2_volume,
                'has_sufficient_margin': True,  # Assume que volumes mínimos sempre cabem
                'total_margin_required': 0,
                'margin_available': 0,
                'method': 'minimum',
                'success': True
            }
            
        except Exception as e:
            logger.error(f"Erro ao calcular volumes mínimos: {e}")
            return self._get_minimum_volumes(symbol1, symbol2, str(e))
    
    def _get_minimum_volumes(self, symbol1: str, symbol2: str, error_msg: str) -> Dict[str, Any]:
        """Retorna volumes padrão em caso de erro"""
        logger.error(f"Usando volumes padrão devido a: {error_msg}")
        
        return {
            'symbol1': symbol1,
            'symbol2': symbol2,
            'symbol1_volume': 0.01,
            'symbol2_volume': 0.01,
            'has_sufficient_margin': True,
            'total_margin_required': 0,
            'margin_available': 0,
            'method': 'default',
            'success': False,
            'error': error_msg
        }
    
    def validate_margin_requirements(self, volumes_info: Dict[str, Any]) -> bool:
        """Valida se há margem suficiente para as posições"""
        try:
            if not volumes_info.get('success', False):
                return False
            
            return volumes_info.get('has_sufficient_margin', False)
            
        except Exception as e:
            logger.error(f"Erro ao validar margem: {e}")
            return False
    
    def adjust_for_existing_exposure(self, symbol1: str, symbol2: str, 
                                   volumes_info: Dict[str, Any]) -> Dict[str, Any]:
        """Ajusta volumes considerando exposição existente"""
        try:
            # TODO: Implementar verificação de exposição existente
            # Por enquanto, retorna volumes inalterados
            
            max_exposure = self.risk_config.get('max_exposure_percent', 10) / 100
            
            # Verifica exposição atual (placeholder)
            current_exposure = 0.05  # 5% - placeholder
            
            if current_exposure >= max_exposure:
                logger.warning(f"Exposição máxima atingida: {current_exposure*100:.1f}%")
                volumes_info['symbol1_volume'] = 0
                volumes_info['symbol2_volume'] = 0
                volumes_info['has_sufficient_margin'] = False
                volumes_info['error'] = 'Exposição máxima atingida'
            
            return volumes_info
            
        except Exception as e:
            logger.error(f"Erro ao ajustar exposição: {e}")
            return volumes_info 