#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script para corrigir símbolos ? na interface do Bancomat 4
Remove emojis problemáticos que aparecem como ? no Windows
"""

import re
import os
from pathlib import Path

def fix_interface_symbols():
    """Corrige símbolos problemáticos na interface"""
    
    interface_file = Path("ui/advanced_interface.py")
    
    if not interface_file.exists():
        print("❌ Arquivo da interface não encontrado!")
        return False
    
    # Lê o arquivo
    with open(interface_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Substituições de emojis problemáticos
    replacements = {
        # Remove prefixos ? que podem estar aparecendo
        r'\?\?\s*': '',
        r'\?\s*(?=\w)': '',
        
        # Substitui emojis que podem estar causando problemas
        '🔍': '[BUSCAR]',
        '🔄': '[ATUALIZAR]', 
        '🧹': '[LIMPAR]',
        '📈': '[EXECUTAR]',
        '🤖': '[AUTO]',
        '🗑️': '[REMOVER]',
        '📊': '[ANALISAR]',
        '📋': '[COPIAR]',
        '⚠️': '[ALERTAS]',
        '📰': '[STATS]',
        '⚙️': '[CONFIG]',
        '💾': '[SALVAR]',
        '🚀': '[INICIAR]',
        '🛑': '[PARAR]',
        '⚡': '[FORCAR]',
        '💱': '[FOREX]',
        
        # Remove possíveis ? restantes nos labels
        r'label="[^"]*\?+[^"]*"': lambda m: m.group().replace('?', ''),
    }
    
    # Aplica substituições
    for pattern, replacement in replacements.items():
        if callable(replacement):
            content = re.sub(pattern, replacement, content)
        else:
            content = content.replace(pattern, replacement)
    
    # Cria backup
    backup_file = interface_file.with_suffix('.py.backup')
    with open(backup_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    # Salva arquivo corrigido
    with open(interface_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ Símbolos da interface corrigidos!")
    print(f"📁 Backup salvo em: {backup_file}")
    
    return True

if __name__ == "__main__":
    print("🔧 Corrigindo símbolos da interface...")
    
    if fix_interface_symbols():
        print("✅ Correção concluída!")
        print("🔄 Reinicie o Bancomat 4 para ver as correções")
    else:
        print("❌ Falha na correção") 