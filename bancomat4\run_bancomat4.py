#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Launcher para Bancomat 4
- Script simplificado para executar o EA
"""

import sys
import os
from pathlib import Path

# Adiciona o diretório atual ao path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def main():
    """Executa o Bancomat 4"""
    print("🚀 Iniciando Bancomat 4...")
    
    try:
        from main import main as bancomat_main
        return bancomat_main()
    except Exception as e:
        print(f"❌ Erro ao iniciar Bancomat 4: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main()) 