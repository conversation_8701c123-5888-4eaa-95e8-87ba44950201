#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
<PERSON><PERSON><PERSON><PERSON> Análise Avançada para Bancomat 4
- Análise de correlação e cointegração
- Cálculo de Z-Score em tempo real
- Geração de sinais de trading
- Análise multi-timeframe
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Any, Optional
from datetime import datetime, timedelta
import asyncio
import threading
import time
from dataclasses import dataclass

try:
    from statsmodels.tsa.stattools import adfuller, coint
    from statsmodels.regression.linear_model import OLS
    import statsmodels.api as sm
    HAS_STATSMODELS = True
except ImportError:
    HAS_STATSMODELS = False
    print("⚠️ statsmodels não encontrado. Análise estatística limitada.")

from utils.logger import get_logger
from utils.config import ConfigManager
from core.mt5_connector import MT5Connector

# Tenta importar position sizing
try:
    from core.position_sizing import PositionSizer
    HAS_POSITION_SIZING = True
except ImportError:
    HAS_POSITION_SIZING = False
    PositionSizer = None

logger = get_logger(__name__)


@dataclass
class Signal:
    """Representa um sinal de trading"""
    pair_name: str
    symbol1: str
    symbol2: str
    signal_type: str  # 'BUY', 'SELL', 'CLOSE'
    z_score: float
    correlation: float
    confidence: float
    score: float
    timestamp: datetime
    timeframe: int
    criteria: Dict[str, Any]
    expires_at: datetime
    # Novos campos para position sizing
    beta: float
    half_life: float  # Half-life do spread (em períodos)
    suggested_volume1: float = 0.01
    suggested_volume2: float = 0.01
    margin_required: float = 0.0
    
    def is_expired(self) -> bool:
        """Verifica se o sinal expirou"""
        return datetime.now() > self.expires_at
    
    def to_dict(self) -> Dict[str, Any]:
        """Converte para dicionário"""
        return {
            'pair_name': self.pair_name,
            'symbol1': self.symbol1,
            'symbol2': self.symbol2,
            'signal_type': self.signal_type,
            'z_score': self.z_score,
            'correlation': self.correlation,
            'confidence': self.confidence,
            'score': self.score,
            'timestamp': self.timestamp.isoformat(),
            'timeframe': self.timeframe,
            'criteria': self.criteria,
            'expires_at': self.expires_at.isoformat(),
            'beta': self.beta,
            'half_life': self.half_life,
            'suggested_volume1': self.suggested_volume1,
            'suggested_volume2': self.suggested_volume2,
            'margin_required': self.margin_required
        }


@dataclass
class PairAnalysis:
    """Resultado completo de análise de um par"""
    pair_name: str
    symbol1: str
    symbol2: str
    correlation: float
    z_score: float
    spread: float
    beta: float
    alpha: float
    adf_pvalue: float
    adf_stat: float
    half_life: float
    is_cointegrated: bool
    score: float
    status: str
    timeframe: int
    period: int
    timestamp: datetime
    criteria_details: Dict[str, Any]


class PairAnalyzer:
    """Sistema de análise avançada de pares forex"""
    
    def __init__(self, config: ConfigManager, mt5: MT5Connector):
        self.config = config
        self.mt5 = mt5
        self.running = False
        
        # Cache de análises
        self.analysis_cache = {}
        self.signals_cache = []
        
        # Configurações
        self.analysis_config = config.get_section('analysis')
        self.signals_config = config.get_section('signals')
        self.risk_config = config.get_section('risk')
        
        # Thread de análise
        self.analysis_thread = None
        
        # Sistema de position sizing
        if HAS_POSITION_SIZING and PositionSizer:
            try:
                self.position_sizer = PositionSizer(config, mt5)
                logger.info("💰 Position sizer integrado ao analisador")
            except Exception as e:
                logger.warning(f"Erro ao inicializar position sizer: {e}")
                self.position_sizer = None
        else:
            self.position_sizer = None
            logger.warning("Position sizer não disponível")
        
        logger.info("🔬 Analisador de pares inicializado")
    
    def start_analysis(self):
        """Inicia análise contínua"""
        if self.running:
            logger.warning("Análise já em execução")
            return
        
        self.running = True
        self.analysis_thread = threading.Thread(target=self._analysis_loop, daemon=True)
        self.analysis_thread.start()
        logger.info("🚀 Análise contínua iniciada")
    
    def stop_analysis(self):
        """Para análise contínua"""
        self.running = False
        if self.analysis_thread:
            self.analysis_thread.join(timeout=5)
        logger.info("🛑 Análise contínua parada")
    
    def _analysis_loop(self):
        """Loop principal de análise"""
        try:
            update_interval = self.analysis_config.get('update_interval', 10)
            
            while self.running:
                try:
                    # Atualiza análises
                    self._update_analysis()
                    
                    # Gera novos sinais
                    self._generate_signals()
                    
                    # Aguarda próximo ciclo
                    time.sleep(update_interval)
                    
                except Exception as e:
                    logger.error(f"Erro no loop de análise: {e}")
                    time.sleep(30)  # Espera mais em caso de erro
                    
        except Exception as e:
            logger.error(f"Erro fatal no loop de análise: {e}")
    
    def _update_analysis(self):
        """Atualiza análises de pares"""
        try:
            if not self.mt5 or not self.mt5.check_connection():
                logger.warning("MT5 não conectado - pulando análise")
                return
            
            # Obtém lista de pares forex
            forex_pairs = self.mt5.get_forex_pairs()
            if not forex_pairs:
                logger.warning("Nenhum par forex encontrado")
                return
            
            logger.info(f"🔍 Iniciando análise de {len(forex_pairs)} pares disponíveis")
            
            # Analisa cada par (limitado para performance)
            max_pairs = 20  # Limita análise simultânea
            pairs_to_analyze = forex_pairs[:max_pairs]
            
            analyses_completed = 0
            for pair_info in pairs_to_analyze:
                try:
                    # pair_info é um objeto SymbolInfo, precisa acessar o atributo 'name'
                    pair_name = pair_info.name
                    logger.debug(f"🔬 Analisando {pair_name}...")
                    
                    analysis = self.analyze_pair(pair_name)
                    if analysis:
                        self.analysis_cache[pair_name] = analysis
                        analyses_completed += 1
                        logger.info(f"✅ {pair_name}: Score {analysis.score:.1f} - Status: {analysis.status}")
                    else:
                        logger.debug(f"❌ {pair_name}: Análise falhou")
                        
                except Exception as e:
                    logger.error(f"Erro ao analisar par {getattr(pair_info, 'name', str(pair_info))}: {e}")
            
            logger.info(f"📊 Análise completa: {analyses_completed}/{len(pairs_to_analyze)} pares analisados com sucesso")
            
        except Exception as e:
            logger.error(f"Erro ao atualizar análises: {e}")
    
    def analyze_pair(self, symbol: str, timeframe: int = 15, period: int = 200) -> Optional[PairAnalysis]:
        """
        Analisa um par específico
        
        Args:
            symbol: Nome do símbolo (ex: 'EURUSD')
            timeframe: Timeframe em minutos
            period: Período de análise
            
        Returns:
            PairAnalysis ou None se não for possível analisar
        """
        try:
            if not HAS_STATSMODELS:
                logger.warning("statsmodels não disponível - análise limitada")
                return None
            
            # Para análise de par único, criamos artificialmente dois símbolos relacionados
            # Por exemplo, EURUSD vs GBPUSD para correlação EUR
            symbol1 = symbol
            
            # Encontra símbolo correlacionado automaticamente
            symbol2 = self._find_correlated_symbol(symbol1)
            if not symbol2:
                logger.debug(f"Não foi possível encontrar símbolo correlacionado para {symbol1}")
                return None
            
            # Obtém dados históricos
            data1 = self._get_price_data(symbol1, timeframe, period)
            data2 = self._get_price_data(symbol2, timeframe, period)
            
            if data1 is None or data2 is None or len(data1) < period or len(data2) < period:
                logger.debug(f"Dados insuficientes para {symbol1}/{symbol2}")
                return None
            
            # Sincroniza dados por timestamp
            data1, data2 = self._synchronize_data(data1, data2)
            
            if len(data1) < 50:  # Mínimo necessário para análise estatística
                return None
            
            # Realiza análise estatística
            analysis_result = self._perform_statistical_analysis(data1, data2, symbol1, symbol2, timeframe, period)
            
            return analysis_result
            
        except Exception as e:
            logger.error(f"Erro ao analisar par {symbol}: {e}")
            return None
    
    def _find_correlated_symbol(self, symbol: str) -> Optional[str]:
        """Encontra símbolo correlacionado para análise de par"""
        try:
            logger.debug(f"🔍 Buscando símbolo correlacionado para {symbol}")
            
            forex_pairs = self.mt5.get_forex_pairs()
            if not forex_pairs:
                logger.debug(f"❌ Nenhum par forex disponível para correlação com {symbol}")
                return None
            
            # Filtra símbolos incompatíveis
            symbol_category = self._get_symbol_category(symbol)
            logger.debug(f"📂 Categoria do símbolo {symbol}: {symbol_category}")
            
            # Estratégias para encontrar par correlacionado
            base_currency = symbol[:3]  # Ex: EUR de EURUSD
            quote_currency = symbol[3:]  # Ex: USD de EURUSD
            
            # 1. Procura outro par com mesma moeda base (APENAS FOREX)
            if symbol_category == 'forex':
                logger.debug(f"🔍 Procurando pares forex com moeda base {base_currency}")
                
                candidates = []
                for pair_info in forex_pairs:
                    pair_name = pair_info.name
                    pair_category = self._get_symbol_category(pair_name)
                    
                    if (len(pair_name) >= 6 and pair_name != symbol and 
                        pair_category == 'forex' and pair_name[:3] == base_currency):
                        candidates.append(pair_name)
                        logger.debug(f"✅ Candidato encontrado (mesma moeda base): {pair_name}")
                
                if candidates:
                    selected = candidates[0]
                    logger.info(f"✅ Símbolo correlacionado selecionado para {symbol}: {selected} (mesma moeda base)")
                    return selected
                
                # 2. Procura par com mesma moeda de cotação (APENAS FOREX)
                logger.debug(f"🔍 Procurando pares forex com moeda de cotação {quote_currency}")
                
                for pair_info in forex_pairs:
                    pair_name = pair_info.name
                    pair_category = self._get_symbol_category(pair_name)
                    
                    if (len(pair_name) >= 6 and pair_name != symbol and 
                        pair_category == 'forex' and pair_name[3:] == quote_currency):
                        logger.info(f"✅ Símbolo correlacionado selecionado para {symbol}: {pair_name} (mesma moeda cotação)")
                        return pair_name
                
                # 3. Fallback para majors conhecidos (APENAS FOREX)
                logger.debug(f"🔍 Usando fallback para majors conhecidos")
                majors = ['EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'AUDUSD', 'USDCAD', 'NZDUSD']
                forex_pair_names = [pair_info.name for pair_info in forex_pairs 
                                  if self._get_symbol_category(pair_info.name) == 'forex']
                
                for major in majors:
                    if major != symbol and major in forex_pair_names:
                        logger.info(f"✅ Símbolo correlacionado selecionado para {symbol}: {major} (major fallback)")
                        return major
            
            # Para outros tipos (crypto, índices, etc.), busca dentro da mesma categoria
            elif symbol_category in ['crypto', 'index', 'commodity']:
                logger.debug(f"🔍 Procurando símbolos da categoria {symbol_category}")
                
                for pair_info in forex_pairs:
                    pair_name = pair_info.name
                    pair_category = self._get_symbol_category(pair_name)
                    
                    if (pair_name != symbol and pair_category == symbol_category):
                        logger.info(f"✅ Símbolo correlacionado selecionado para {symbol}: {pair_name} (mesma categoria)")
                        return pair_name
            
            logger.warning(f"❌ Nenhum símbolo correlacionado encontrado para {symbol} (categoria: {symbol_category})")
            return None
            
        except Exception as e:
            logger.error(f"Erro ao encontrar símbolo correlacionado para {symbol}: {e}")
            return None
    
    def _get_symbol_category(self, symbol: str) -> str:
        """Determina a categoria de um símbolo"""
        symbol = symbol.upper()
        
        # Lista de cryptos conhecidas
        crypto_symbols = [
            'BTC', 'ETH', 'LTC', 'BCH', 'XRP', 'ADA', 'DOT', 'DOGE', 'SHIB',
            'SOL', 'AVAX', 'MATIC', 'LINK', 'UNI', 'ATOM', 'FTT', 'NEAR'
        ]
        
        # Lista de índices conhecidos
        index_symbols = [
            'US30', 'US500', 'US100', 'DE30', 'UK100', 'FR40', 'JP225',
            'AUS200', 'HK50', 'CA60', 'ES35', 'NAS100', 'SPX500', 'DJI'
        ]
        
        # Lista de commodities conhecidas
        commodity_symbols = [
            'GOLD', 'SILVER', 'OIL', 'BRENT', 'GAS', 'COPPER', 'PLATINUM',
            'XAUUSD', 'XAGUSD', 'USOIL', 'UKOIL'
        ]
        
        # Verifica cryptos primeiro (mais específico)
        for crypto in crypto_symbols:
            if crypto in symbol:
                return 'crypto'
        
        # Verifica índices
        for index in index_symbols:
            if index in symbol:
                return 'index'
        
        # Verifica commodities
        for commodity in commodity_symbols:
            if commodity in symbol:
                return 'commodity'
        
        # Lista EXPANDIDA de moedas forex (mais permissivo)
        forex_currencies = {
            'USD', 'EUR', 'GBP', 'JPY', 'CHF', 'CAD', 'AUD', 'NZD',
            'SEK', 'NOK', 'DKK', 'PLN', 'CZK', 'HUF', 'TRY', 'ZAR',
            'MXN', 'SGD', 'HKD', 'CNY', 'RUB', 'INR', 'KRW', 'BRL',
            'THB', 'MYR', 'PHP', 'TWD', 'ILS', 'CLP', 'PEN', 'COP'
        }
        
        # Verifica se é forex (6+ caracteres com moedas conhecidas)
        if len(symbol) >= 6:
            base = symbol[:3]
            quote = symbol[3:6]
            if base in forex_currencies and quote in forex_currencies:
                return 'forex'
        
        # FALLBACK: Se não identificou especificamente, assume forex
        # Isso é mais permissivo e evita rejeitar pares válidos
        logger.debug(f"🔄 Categoria não identificada para {symbol}, assumindo 'forex' como fallback")
        return 'forex'
    
    def _get_price_data(self, symbol: str, timeframe: int, period: int) -> Optional[pd.DataFrame]:
        """Obtém dados de preço históricos"""
        try:
            import MetaTrader5 as mt5
            
            # Calcula timeframe MT5
            if timeframe == 1:
                mt5_timeframe = mt5.TIMEFRAME_M1
            elif timeframe == 5:
                mt5_timeframe = mt5.TIMEFRAME_M5
            elif timeframe == 15:
                mt5_timeframe = mt5.TIMEFRAME_M15
            elif timeframe == 30:
                mt5_timeframe = mt5.TIMEFRAME_M30
            elif timeframe == 60:
                mt5_timeframe = mt5.TIMEFRAME_H1
            else:
                mt5_timeframe = mt5.TIMEFRAME_M15
            
            # Obtém dados
            rates = mt5.copy_rates_from_pos(symbol, mt5_timeframe, 0, period + 50)  # +50 para margem
            
            if rates is None or len(rates) < period:
                return None
            
            # Converte para DataFrame
            df = pd.DataFrame(rates)
            df['time'] = pd.to_datetime(df['time'], unit='s')
            df.set_index('time', inplace=True)
            
            return df[-period:]  # Retorna apenas o período solicitado
            
        except Exception as e:
            logger.error(f"Erro ao obter dados de {symbol}: {e}")
            return None
    
    def _synchronize_data(self, data1: pd.DataFrame, data2: pd.DataFrame) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """Sincroniza dados por timestamp"""
        try:
            # Encontra timestamps comuns
            common_times = data1.index.intersection(data2.index)
            
            if len(common_times) == 0:
                raise ValueError("Nenhum timestamp comum encontrado")
            
            # Filtra dados para timestamps comuns
            sync_data1 = data1.loc[common_times]
            sync_data2 = data2.loc[common_times]
            
            return sync_data1, sync_data2
            
        except Exception as e:
            logger.error(f"Erro ao sincronizar dados: {e}")
            return data1, data2
    
    def _perform_statistical_analysis(self, data1: pd.DataFrame, data2: pd.DataFrame, 
                                    symbol1: str, symbol2: str, timeframe: int, period: int) -> PairAnalysis:
        """Realiza análise estatística completa"""
        try:
            # Extrai preços de fechamento
            prices1 = data1['close'].values
            prices2 = data2['close'].values
            
            # 1. Correlação
            correlation = np.corrcoef(prices1, prices2)[0, 1]
            
            # 2. Regressão linear (para beta e alpha)
            X = sm.add_constant(prices2)  # Adiciona constante
            model = OLS(prices1, X).fit()
            alpha = model.params[0]
            beta = model.params[1]
            
            # 3. Spread
            spread = prices1 - beta * prices2 - alpha
            spread_current = spread[-1]
            spread_mean = np.mean(spread)
            spread_std = np.std(spread)
            
            # 4. Z-Score
            z_score = (spread_current - spread_mean) / spread_std if spread_std > 0 else 0
            
            # 5. Teste de cointegração
            if len(prices1) >= 30 and len(prices2) >= 30:
                try:
                    # Teste ADF no spread
                    adf_result = adfuller(spread, regression='c')
                    adf_stat = adf_result[0]
                    adf_pvalue = adf_result[1]
                    
                    # Teste de cointegração Engle-Granger
                    coint_t, coint_pvalue, _ = coint(prices1, prices2)
                    
                    # Usa o p-value mais conservador
                    final_pvalue = max(adf_pvalue, coint_pvalue)
                    
                except Exception as e:
                    logger.debug(f"Erro nos testes de cointegração: {e}")
                    adf_stat = 0
                    final_pvalue = 1.0
            else:
                adf_stat = 0
                final_pvalue = 1.0
            
            # 6. Half-life
            half_life = self._calculate_half_life(spread)
            
            # 7. Determina cointegração
            is_cointegrated = final_pvalue < self.signals_config.get('min_cointegration_pvalue', 0.05)
            
            # 8. Avalia critérios
            criteria_details = self._evaluate_criteria(z_score, correlation, final_pvalue, half_life, beta)
            
            # 9. Calcula score final
            score = self._calculate_score(z_score, correlation, final_pvalue, half_life, criteria_details)
            
            # 10. Determina status
            status = self._determine_status(z_score, score, criteria_details)
            
            # Cria resultado
            pair_name = f"{symbol1}_{symbol2}"
            
            analysis = PairAnalysis(
                pair_name=pair_name,
                symbol1=symbol1,
                symbol2=symbol2,
                correlation=correlation,
                z_score=z_score,
                spread=spread_current,
                beta=beta,
                alpha=alpha,
                adf_pvalue=final_pvalue,
                adf_stat=adf_stat,
                half_life=half_life,
                is_cointegrated=is_cointegrated,
                score=score,
                status=status,
                timeframe=timeframe,
                period=period,
                timestamp=datetime.now(),
                criteria_details=criteria_details
            )
            
            return analysis
            
        except Exception as e:
            logger.error(f"Erro na análise estatística: {e}")
            raise
    
    def _calculate_half_life(self, spread: np.ndarray) -> float:
        """Calcula half-life do spread"""
        try:
            if len(spread) < 10:
                return 999.0
            
            # Regressão AR(1) no spread
            spread_lag = np.roll(spread, 1)[1:]  # Remove primeiro elemento
            spread_curr = spread[1:]
            
            if len(spread_lag) < 5:
                return 999.0
            
            # Modelo: spread(t) = c + λ * spread(t-1) + ε
            X = sm.add_constant(spread_lag)
            model = OLS(spread_curr, X).fit()
            
            lambda_coeff = model.params[1]
            
            if lambda_coeff >= 1 or lambda_coeff <= 0:
                return 999.0  # Não convergente
            
            # Half-life = -ln(2) / ln(λ)
            half_life = -np.log(2) / np.log(lambda_coeff)
            
            # Limita valores extremos
            return max(1, min(half_life, 999))
            
        except Exception as e:
            logger.debug(f"Erro ao calcular half-life: {e}")
            return 999.0
    
    def _evaluate_criteria(self, z_score: float, correlation: float, adf_pvalue: float, half_life: float, beta: float = None) -> Dict[str, Any]:
        """Avalia critérios de trading"""
        criteria = {}
        
        # Z-Score
        min_zscore = self.signals_config.get('min_zscore', 2.0)
        max_zscore = self.signals_config.get('max_zscore', 4.0)
        criteria['zscore'] = {
            'value': abs(z_score),
            'min_threshold': min_zscore,
            'max_threshold': max_zscore,
            'passes': min_zscore <= abs(z_score) <= max_zscore
        }
        
        # Correlação - critério ajustado
        min_corr = self.signals_config.get('min_correlation', 0.60)  # Reduzido de 0.70 para 0.60
        max_corr = self.signals_config.get('max_correlation', 0.95)
        criteria['correlation'] = {
            'value': abs(correlation),
            'min_threshold': min_corr,
            'max_threshold': max_corr,
            'passes': min_corr <= abs(correlation) <= max_corr
        }
        
        # Cointegração - critério ajustado
        max_pvalue = self.signals_config.get('min_cointegration_pvalue', 0.10)  # Aumentado de 0.05 para 0.10
        criteria['cointegration'] = {
            'value': adf_pvalue,
            'threshold': max_pvalue,
            'passes': adf_pvalue <= max_pvalue
        }
        
        # Half-life
        min_hl = self.signals_config.get('min_half_life', 5)
        max_hl = self.signals_config.get('max_half_life', 96)
        criteria['half_life'] = {
            'value': half_life,
            'min_threshold': min_hl,
            'max_threshold': max_hl,
            'passes': min_hl <= half_life <= max_hl
        }
        
        # Beta - CRITÉRIO CRÍTICO para compatibilidade de pares
        if beta is not None:
            max_beta = self.risk_config.get('max_beta_threshold', 10.0)  # Configurável
            min_beta = self.risk_config.get('min_beta_threshold', 0.1)   # Configurável
            beta_abs = abs(beta)
            criteria['beta'] = {
                'value': beta_abs,
                'min_threshold': min_beta,
                'max_threshold': max_beta,
                'passes': min_beta <= beta_abs <= max_beta
            }
            
            # Log de warning para betas extremos
            if beta_abs > max_beta:
                logger.warning(f"Beta extremo detectado: {beta:.2f} - Par rejeitado por incompatibilidade (limite: {max_beta})")
            elif beta_abs < min_beta:
                logger.warning(f"Beta muito baixo detectado: {beta:.6f} - Par rejeitado por baixa correlação (limite: {min_beta})")
        
        # Conta critérios aprovados
        passed_count = sum(1 for c in criteria.values() if c['passes'])
        total_count = len(criteria) - 1  # -1 porque summary não conta
        criteria['summary'] = {
            'passed_count': passed_count,
            'total_count': total_count,
            'all_pass': passed_count == total_count
        }
        
        return criteria
    
    def _calculate_score(self, z_score: float, correlation: float, adf_pvalue: float, 
                        half_life: float, criteria: Dict[str, Any]) -> float:
        """Calcula score final do par"""
        try:
            score = 0.0
            
            # Score do Z-Score (0-30 pontos)
            zscore_abs = abs(z_score)
            if 2.0 <= zscore_abs <= 4.0:
                # Score máximo em z=2.5, decai nas bordas
                optimal_z = 2.5
                if zscore_abs <= optimal_z:
                    score += 30 * (zscore_abs / optimal_z)
                else:
                    score += 30 * (1 - (zscore_abs - optimal_z) / (4.0 - optimal_z))
            
            # Score da correlação (0-25 pontos)
            corr_abs = abs(correlation)
            if corr_abs >= 0.70:
                score += 25 * min(corr_abs / 0.90, 1.0)  # Máximo em 90%
            
            # Score da cointegração (0-25 pontos)
            if adf_pvalue <= 0.05:
                # Quanto menor o p-value, melhor
                score += 25 * (1 - adf_pvalue / 0.05)
            
            # Score do half-life (0-20 pontos)
            if 5 <= half_life <= 96:
                # Score máximo entre 10-30 períodos
                if 10 <= half_life <= 30:
                    score += 20
                elif half_life < 10:
                    score += 20 * (half_life / 10)
                else:  # half_life > 30
                    score += 20 * (1 - (half_life - 30) / (96 - 30))
            
            return min(score, 100.0)  # Limita a 100
            
        except Exception as e:
            logger.error(f"Erro ao calcular score: {e}")
            return 0.0
    
    def _determine_status(self, z_score: float, score: float, criteria: Dict[str, Any]) -> str:
        """Determina status do par"""
        if not criteria.get('summary', {}).get('all_pass', False):
            return "REJEITADO"
        
        min_score = self.signals_config.get('min_score', 60)  # Ajustado de 65 para 60
        if score < min_score:
            return "SCORE_BAIXO"
        
        if abs(z_score) < 2.0:
            return "NORMAL"
        elif z_score > 2.0:
            return "SELL_SIGNAL"  # Symbol1 caro, symbol2 barato
        else:  # z_score < -2.0
            return "BUY_SIGNAL"   # Symbol1 barato, symbol2 caro
    
    def _generate_signals(self):
        """Gera novos sinais baseados nas análises"""
        try:
            # PRIMEIRO: Limpa sinais expirados
            self._cleanup_expired_signals()
            
            new_signals = []
            updated_signals = []
            rejected_by_exposure = []
            
            for pair_name, analysis in self.analysis_cache.items():
                if analysis.status in ['BUY_SIGNAL', 'SELL_SIGNAL']:
                    # Verifica se já existe sinal ativo para este par
                    existing_signal = next((s for s in self.signals_cache 
                                          if s.pair_name == pair_name and not s.is_expired()), None)
                    
                    if existing_signal:
                        # ATUALIZA sinal existente em vez de criar novo
                        signal_type = 'SELL' if analysis.status == 'SELL_SIGNAL' else 'BUY'
                        
                        # Verifica se mudou significativamente para justificar atualização
                        z_score_change = abs(existing_signal.z_score - analysis.z_score)
                        score_change = abs(existing_signal.score - analysis.score)
                        
                        # Só atualiza se houver mudança significativa (evita updates desnecessários)
                        if z_score_change > 0.1 or score_change > 5.0:
                            # Atualiza campos do sinal existente
                            existing_signal.z_score = analysis.z_score
                            existing_signal.correlation = analysis.correlation
                            existing_signal.score = analysis.score
                            existing_signal.confidence = min(analysis.score / 100.0, 1.0)
                            existing_signal.signal_type = signal_type
                            existing_signal.criteria = analysis.criteria_details
                            existing_signal.beta = analysis.beta
                            existing_signal.half_life = analysis.half_life
                            existing_signal.timestamp = analysis.timestamp  # Atualiza timestamp
                            
                            # Recalcula volumes se position sizer disponível
                            if self.position_sizer:
                                try:
                                    volumes_info = self.position_sizer.calculate_volumes(
                                        analysis.symbol1, 
                                        analysis.symbol2, 
                                        analysis.beta,
                                        analysis.z_score,
                                        existing_signal.confidence
                                    )
                                    
                                    if volumes_info.get('success', False):
                                        existing_signal.suggested_volume1 = volumes_info.get('symbol1_volume', 0.01)
                                        existing_signal.suggested_volume2 = volumes_info.get('symbol2_volume', 0.01)
                                        existing_signal.margin_required = volumes_info.get('total_margin_required', 0.0)
                                        
                                except Exception as e:
                                    logger.error(f"Erro ao recalcular volumes para {pair_name}: {e}")
                            
                            updated_signals.append(existing_signal)
                            logger.debug(f"🔄 Sinal atualizado: {pair_name} - Z:{analysis.z_score:.3f}, Score:{analysis.score:.1f}")
                    else:
                        # NOVO: Verifica exposição por moeda antes de criar sinal
                        can_create_signal = self._check_currency_exposure_for_signal(analysis)
                        
                        if can_create_signal['allowed']:
                            # Cria novo sinal apenas se não existe e exposição permite
                            signal = self._create_signal(analysis)
                            if signal:
                                new_signals.append(signal)
                                self.signals_cache.append(signal)
                                logger.info(f"🔔 Novo sinal criado: {signal.pair_name} - {signal.signal_type}")
                                
                                # Log de warnings se houver
                                if can_create_signal.get('warnings'):
                                    for warning in can_create_signal['warnings']:
                                        logger.warning(f"⚠️ {warning['message']}")
                        else:
                            rejected_by_exposure.append((pair_name, can_create_signal))
                            logger.debug(f"❌ Sinal {pair_name} não criado por exposição: {can_create_signal['reason']}")
            
            if new_signals:
                logger.info(f"🔔 {len(new_signals)} novos sinais gerados")
                
            if updated_signals:
                logger.info(f"🔄 {len(updated_signals)} sinais atualizados")
                
            if rejected_by_exposure:
                logger.info(f"🚫 {len(rejected_by_exposure)} sinais rejeitados por controle de exposição")
                
        except Exception as e:
            logger.error(f"Erro ao gerar sinais: {e}")
    
    def _check_currency_exposure_for_signal(self, analysis: 'PairAnalysis') -> Dict[str, Any]:
        """
        Verifica se um sinal pode ser criado baseado na exposição por moeda
        
        Args:
            analysis: Análise do par
            
        Returns:
            Dicionário com resultado da verificação
        """
        try:
            # Se não temos AutoTrader ou exposure manager disponível, permite
            # (para casos onde analyzer é usado independentemente)
            if not hasattr(self, '_exposure_manager') or not self._exposure_manager:
                return {'allowed': True, 'reason': 'Controle de exposição não disponível'}
            
            # Calcula volumes sugeridos
            suggested_volume1 = 0.01
            suggested_volume2 = 0.01
            
            if self.position_sizer:
                try:
                    volumes_info = self.position_sizer.calculate_volumes(
                        analysis.symbol1, 
                        analysis.symbol2, 
                        analysis.beta,
                        analysis.z_score,
                        min(analysis.score / 100.0, 1.0)
                    )
                    
                    if volumes_info.get('success', False):
                        suggested_volume1 = volumes_info.get('symbol1_volume', 0.01)
                        suggested_volume2 = volumes_info.get('symbol2_volume', 0.01)
                        
                except Exception as e:
                    logger.debug(f"Erro ao calcular volumes para verificação de exposição: {e}")
            
            # Verifica com o exposure manager
            exposure_check = self._exposure_manager.can_open_trade(
                analysis.pair_name,
                suggested_volume1,
                suggested_volume2
            )
            
            return exposure_check
            
        except Exception as e:
            logger.error(f"Erro ao verificar exposição para sinal: {e}")
            return {'allowed': True, 'reason': 'Erro na verificação de exposição'}
    
    def set_exposure_manager(self, exposure_manager):
        """Define o gerenciador de exposição (usado pelo AutoTrader)"""
        self._exposure_manager = exposure_manager
        logger.info("🔗 Gerenciador de exposição conectado ao analyzer")
    
    def _create_signal(self, analysis: PairAnalysis) -> Optional[Signal]:
        """Cria sinal baseado na análise"""
        try:
            signal_type = 'SELL' if analysis.status == 'SELL_SIGNAL' else 'BUY'
            
            # Calcula confiança baseada no score e critérios
            confidence = min(analysis.score / 100.0, 1.0)
            
            # Tempo de expiração
            expiry_seconds = self.signals_config.get('signal_expiry', 300)
            expires_at = datetime.now() + timedelta(seconds=expiry_seconds)
            
            # Calcula volumes usando position sizer
            suggested_volume1 = 0.01
            suggested_volume2 = 0.01
            margin_required = 0.0
            
            if self.position_sizer:
                try:
                    volumes_info = self.position_sizer.calculate_volumes(
                        analysis.symbol1, 
                        analysis.symbol2, 
                        analysis.beta,
                        analysis.z_score,
                        confidence
                    )
                    
                    if volumes_info.get('success', False):
                        suggested_volume1 = volumes_info.get('symbol1_volume', 0.01)
                        suggested_volume2 = volumes_info.get('symbol2_volume', 0.01)
                        margin_required = volumes_info.get('total_margin_required', 0.0)
                        
                        logger.info(f"💰 Volumes calculados para {analysis.pair_name}: "
                                  f"{analysis.symbol1}={suggested_volume1:.4f}, "
                                  f"{analysis.symbol2}={suggested_volume2:.4f}")
                    else:
                        logger.warning(f"Erro no cálculo de volumes para {analysis.pair_name}: "
                                     f"{volumes_info.get('error', 'Erro desconhecido')}")
                        
                except Exception as e:
                    logger.error(f"Erro ao calcular volumes para {analysis.pair_name}: {e}")
            
            signal = Signal(
                pair_name=analysis.pair_name,
                symbol1=analysis.symbol1,
                symbol2=analysis.symbol2,
                signal_type=signal_type,
                z_score=analysis.z_score,
                correlation=analysis.correlation,
                confidence=confidence,
                score=analysis.score,
                timestamp=analysis.timestamp,
                timeframe=analysis.timeframe,
                criteria=analysis.criteria_details,
                expires_at=expires_at,
                beta=analysis.beta,
                half_life=analysis.half_life,
                suggested_volume1=suggested_volume1,
                suggested_volume2=suggested_volume2,
                margin_required=margin_required
            )
            
            return signal
            
        except Exception as e:
            logger.error(f"Erro ao criar sinal: {e}")
            return None
    
    def _cleanup_expired_signals(self):
        """Remove sinais expirados e duplicatas"""
        try:
            before_count = len(self.signals_cache)
            
            # Remove sinais expirados
            valid_signals = [s for s in self.signals_cache if not s.is_expired()]
            
            # Remove duplicatas por par (mantém apenas o mais recente)
            unique_signals = {}
            for signal in valid_signals:
                if signal.pair_name not in unique_signals:
                    unique_signals[signal.pair_name] = signal
                else:
                    # Mantém o mais recente
                    if signal.timestamp > unique_signals[signal.pair_name].timestamp:
                        unique_signals[signal.pair_name] = signal
            
            self.signals_cache = list(unique_signals.values())
            after_count = len(self.signals_cache)
            
            if before_count > after_count:
                removed_count = before_count - after_count
                logger.info(f"🧹 {removed_count} sinais expirados/duplicados removidos")
                
        except Exception as e:
            logger.error(f"Erro ao limpar sinais expirados: {e}")
    
    def force_cleanup_duplicate_signals(self):
        """Força limpeza de sinais duplicados"""
        try:
            logger.info("🧹 Forçando limpeza de sinais duplicados...")
            
            before_count = len(self.signals_cache)
            
            # Agrupa sinais por par
            signals_by_pair = {}
            for signal in self.signals_cache:
                if not signal.is_expired():  # Só considera sinais válidos
                    if signal.pair_name not in signals_by_pair:
                        signals_by_pair[signal.pair_name] = []
                    signals_by_pair[signal.pair_name].append(signal)
            
            # Mantém apenas o sinal mais recente de cada par
            cleaned_signals = []
            for pair_name, signals_list in signals_by_pair.items():
                if signals_list:
                    # Ordena por timestamp e pega o mais recente
                    latest_signal = max(signals_list, key=lambda s: s.timestamp)
                    cleaned_signals.append(latest_signal)
            
            self.signals_cache = cleaned_signals
            after_count = len(self.signals_cache)
            
            removed_count = before_count - after_count
            logger.info(f"✅ Limpeza concluída: {removed_count} sinais duplicados removidos")
            logger.info(f"📊 Sinais únicos restantes: {after_count}")
            
            return removed_count
            
        except Exception as e:
            logger.error(f"Erro na limpeza forçada: {e}")
            return 0
    
    def get_active_signals(self) -> List[Signal]:
        """Retorna sinais ativos"""
        return [s for s in self.signals_cache if not s.is_expired()]
    
    def get_pair_analysis(self, pair_name: str) -> Optional[PairAnalysis]:
        """Retorna análise de um par específico"""
        return self.analysis_cache.get(pair_name)
    
    def get_all_analyses(self) -> Dict[str, 'PairAnalysis']:
        """
        Retorna todas as análises realizadas
        
        Returns:
            Dicionário com pair_name -> PairAnalysis
        """
        return self.analysis_cache.copy()
    
    def force_analysis_update(self):
        """Força uma atualização imediata da análise"""
        try:
            logger.info("🔄 Forçando atualização da análise...")
            self._update_analysis()
            logger.info("✅ Atualização forçada concluída")
        except Exception as e:
            logger.error(f"Erro ao forçar atualização: {e}")
    
    def force_full_analysis(self):
        """Força análise completa de todos os pares disponíveis"""
        try:
            logger.info("🔍 Iniciando análise completa...")
            
            # Limpa cache existente
            self.analysis_cache.clear()
            
            # Força análise de todos os pares
            self._update_analysis()
            
            logger.info("✅ Análise completa finalizada")
        except Exception as e:
            logger.error(f"Erro na análise completa: {e}") 