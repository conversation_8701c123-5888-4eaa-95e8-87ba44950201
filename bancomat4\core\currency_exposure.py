#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Sistema de Gestão de Exposição por Moeda - Bancomat 4
- Controla exposição total por moeda em trades simultâneos
- Previne sobre-exposição a uma única moeda
- Monitora volume total e número de trades por moeda
"""

import json
import re
from pathlib import Path
from typing import Dict, List, Set, Tuple, Optional
from datetime import datetime
from dataclasses import dataclass

from utils.logger import get_logger
from utils.config import ConfigManager

logger = get_logger(__name__)


@dataclass
class CurrencyExposure:
    """Representa exposição de uma moeda específica"""
    currency: str
    total_volume: float = 0.0
    trade_count: int = 0
    positions: List[Dict] = None
    
    def __post_init__(self):
        if self.positions is None:
            self.positions = []
    
    @property
    def risk_level(self) -> str:
        """Calcula nível de risco baseado no volume total"""
        if self.total_volume <= 0.02:
            return "LOW"
        elif self.total_volume <= 0.05:
            return "MEDIUM"
        elif self.total_volume <= 0.08:
            return "HIGH"
        else:
            return "CRITICAL"


class CurrencyExposureManager:
    """Gerenciador de exposição por moeda"""
    
    def __init__(self, config: ConfigManager):
        self.config = config
        
        # Configurações de limites
        risk_config = config.get_section('risk')
        self.max_trades_per_currency = risk_config.get('max_trades_per_currency', 2)
        self.max_volume_per_currency = risk_config.get('max_volume_per_currency', 0.08)
        self.warning_threshold = risk_config.get('currency_warning_threshold', 0.05)
        
        # Cache de exposições
        self.exposures: Dict[str, CurrencyExposure] = {}
        
        # Arquivo de persistência
        self.exposure_file = Path("data") / "currency_exposures.json"
        self.exposure_file.parent.mkdir(exist_ok=True)
        
        logger.info("💰 Sistema de gestão de exposição de moedas inicializado")
        logger.info(f"📊 Limites: {self.max_trades_per_currency} trades, {self.max_volume_per_currency:.3f} volume por moeda")
    
    def extract_currencies_from_pair(self, pair_name: str) -> Set[str]:
        """Extrai moedas de um nome de par (ex: EURUSD_GBPJPY -> EUR,USD,GBP,JPY)"""
        try:
            # Remove underscores e separadores
            clean_pair = pair_name.replace('_', '').replace('-', '').upper()
            
            # Lista de moedas conhecidas (ordenadas por tamanho para pegar códigos de 3 caracteres primeiro)
            known_currencies = [
                'EUR', 'USD', 'GBP', 'JPY', 'CHF', 'CAD', 'AUD', 'NZD',
                'SEK', 'NOK', 'DKK', 'PLN', 'CZK', 'HUF', 'TRY', 'ZAR',
                'SGD', 'HKD', 'MXN', 'CNH', 'INR', 'KRW', 'THB', 'MYR'
            ]
            
            currencies = set()
            position = 0
            
            while position < len(clean_pair):
                found = False
                # Tenta encontrar uma moeda conhecida na posição atual
                for currency in known_currencies:
                    if clean_pair[position:position+len(currency)] == currency:
                        currencies.add(currency)
                        position += len(currency)
                        found = True
                        break
                
                if not found:
                    position += 1  # Avança se não encontrou moeda conhecida
            
            # Fallback: se não encontrou moedas, tenta padrão XXXYYY (6 caracteres)
            if not currencies and len(clean_pair) >= 6:
                currencies.add(clean_pair[:3])
                currencies.add(clean_pair[3:6])
            
            return currencies
            
        except Exception as e:
            logger.error(f"Erro ao extrair moedas do par {pair_name}: {e}")
            return set()
    
    def check_exposure_limits(self, pair_name: str, volume1: float, volume2: float) -> Tuple[bool, str]:
        """Verifica se um novo trade violaria os limites de exposição"""
        try:
            currencies = self.extract_currencies_from_pair(pair_name)
            if not currencies:
                return True, "Não foi possível extrair moedas do par"
            
            volumes = [volume1, volume2]
            currency_list = list(currencies)
            
            # Verifica cada moeda envolvida
            for i, currency in enumerate(currency_list):
                if i >= len(volumes):
                    break
                    
                volume = volumes[i]
                current_exposure = self.exposures.get(currency, CurrencyExposure(currency))
                
                # Verifica limite de trades
                if current_exposure.trade_count >= self.max_trades_per_currency:
                    return False, f"Limite de trades para {currency} atingido: {current_exposure.trade_count}/{self.max_trades_per_currency}"
                
                # Verifica limite de volume
                projected_volume = current_exposure.total_volume + volume
                if projected_volume > self.max_volume_per_currency:
                    return False, f"Limite de volume para {currency} seria excedido: {projected_volume:.3f}/{self.max_volume_per_currency:.3f}"
            
            return True, "Limites de exposição respeitados"
            
        except Exception as e:
            logger.error(f"Erro ao verificar limites de exposição: {e}")
            return False, f"Erro na verificação: {e}"
    
    def add_trade_exposure(self, trade_id: str, pair_name: str, volume1: float, volume2: float, symbol1: str = "", symbol2: str = ""):
        """Adiciona exposição de um novo trade"""
        try:
            currencies = self.extract_currencies_from_pair(pair_name)
            if not currencies:
                logger.warning(f"Não foi possível extrair moedas do par {pair_name}")
                return
            
            volumes = [volume1, volume2]
            symbols = [symbol1, symbol2]
            currency_list = list(currencies)
            
            # Adiciona exposição para cada moeda
            for i, currency in enumerate(currency_list):
                if i >= len(volumes):
                    break
                
                volume = volumes[i]
                symbol = symbols[i] if i < len(symbols) else ""
                
                if currency not in self.exposures:
                    self.exposures[currency] = CurrencyExposure(currency)
                
                exposure = self.exposures[currency]
                exposure.total_volume += volume
                exposure.trade_count += 1
                exposure.positions.append({
                    'trade_id': trade_id,
                    'pair_name': pair_name,
                    'symbol': symbol,
                    'volume': volume,
                    'timestamp': datetime.now().isoformat()
                })
                
                logger.info(f"💰 Exposição {currency}: {exposure.total_volume:.4f} volume, {exposure.trade_count} trades ({exposure.risk_level})")
            
            # Salva estado
            self.save_exposures()
            
        except Exception as e:
            logger.error(f"Erro ao adicionar exposição do trade: {e}")
    
    def remove_trade_exposure(self, trade_id: str):
        """Remove exposição de um trade fechado"""
        try:
            for currency, exposure in self.exposures.items():
                # Remove posições deste trade
                positions_to_remove = []
                for i, position in enumerate(exposure.positions):
                    if position.get('trade_id') == trade_id:
                        exposure.total_volume -= position.get('volume', 0)
                        exposure.trade_count -= 1
                        positions_to_remove.append(i)
                
                # Remove posições da lista (em ordem reversa para não afetar índices)
                for i in reversed(positions_to_remove):
                    del exposure.positions[i]
                
                # Remove exposição se não há mais posições
                if exposure.trade_count <= 0:
                    exposure.total_volume = 0.0
                    exposure.trade_count = 0
                    exposure.positions = []
            
            # Remove exposições vazias
            empty_currencies = [curr for curr, exp in self.exposures.items() if exp.trade_count == 0]
            for currency in empty_currencies:
                del self.exposures[currency]
            
            # Salva estado
            self.save_exposures()
            
            if empty_currencies:
                logger.info(f"💰 Exposições removidas para: {', '.join(empty_currencies)}")
            
        except Exception as e:
            logger.error(f"Erro ao remover exposição do trade: {e}")
    
    def get_exposure_summary(self) -> Dict[str, Dict]:
        """Retorna resumo de todas as exposições"""
        summary = {}
        
        for currency, exposure in self.exposures.items():
            summary[currency] = {
                'currency': currency,
                'total_volume': exposure.total_volume,
                'trade_count': exposure.trade_count,
                'risk_level': exposure.risk_level,
                'utilization_pct': (exposure.total_volume / self.max_volume_per_currency) * 100,
                'trades_utilization_pct': (exposure.trade_count / self.max_trades_per_currency) * 100,
                'positions': exposure.positions
            }
        
        return summary
    
    def get_high_risk_currencies(self) -> List[str]:
        """Retorna moedas com exposição alta ou crítica"""
        high_risk = []
        
        for currency, exposure in self.exposures.items():
            if exposure.risk_level in ['HIGH', 'CRITICAL']:
                high_risk.append(currency)
        
        return high_risk
    
    def load_exposures(self):
        """Carrega exposições do arquivo"""
        try:
            if not self.exposure_file.exists():
                logger.info("📁 Arquivo de exposições não existe, iniciando com dados limpos")
                return
            
            with open(self.exposure_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Reconstrói exposições
            for currency_data in data.get('exposures', []):
                currency = currency_data['currency']
                exposure = CurrencyExposure(
                    currency=currency,
                    total_volume=currency_data.get('total_volume', 0.0),
                    trade_count=currency_data.get('trade_count', 0),
                    positions=currency_data.get('positions', [])
                )
                self.exposures[currency] = exposure
            
            logger.info(f"📂 {len(self.exposures)} exposições de moedas carregadas")
            
            # Log do estado atual
            for currency, exposure in self.exposures.items():
                logger.info(f"💰 {currency}: {exposure.total_volume:.4f} volume, {exposure.trade_count} trades ({exposure.risk_level})")
            
        except Exception as e:
            logger.error(f"Erro ao carregar exposições: {e}")
            self.exposures = {}
    
    def save_exposures(self):
        """Salva exposições no arquivo"""
        try:
            # Prepara dados para serialização
            exposures_data = []
            for currency, exposure in self.exposures.items():
                exposures_data.append({
                    'currency': currency,
                    'total_volume': exposure.total_volume,
                    'trade_count': exposure.trade_count,
                    'positions': exposure.positions
                })
            
            data = {
                'timestamp': datetime.now().isoformat(),
                'exposures': exposures_data,
                'config': {
                    'max_trades_per_currency': self.max_trades_per_currency,
                    'max_volume_per_currency': self.max_volume_per_currency,
                    'warning_threshold': self.warning_threshold
                }
            }
            
            with open(self.exposure_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            logger.debug(f"💾 Exposições salvas em {self.exposure_file}")
            
        except Exception as e:
            logger.error(f"Erro ao salvar exposições: {e}")
    
    def clear_all_exposures(self):
        """Limpa todas as exposições (para reset manual)"""
        self.exposures = {}
        self.save_exposures()
        logger.info("🧹 Todas as exposições de moedas foram limpas")
    
    def validate_exposures_with_mt5(self, mt5_connector):
        """Valida exposições contra posições reais do MT5"""
        try:
            if not mt5_connector or not mt5_connector.check_connection():
                logger.warning("MT5 não conectado - validação de exposições pulada")
                return
            
            # Obtém posições abertas do MT5
            positions = mt5_connector.get_open_positions()
            
            if not positions:
                # Se não há posições no MT5, limpa exposições
                if self.exposures:
                    logger.warning("⚠️ Nenhuma posição no MT5, mas exposições registradas - limpando")
                    self.clear_all_exposures()
                return
            
            # TODO: Implementar validação mais detalhada se necessário
            logger.info(f"✅ Validação de exposições: {len(positions)} posições MT5, {len(self.exposures)} exposições registradas")
            
        except Exception as e:
            logger.error(f"Erro na validação de exposições: {e}") 