#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Correção do Position Sizing - Progressão Suave
- Remove thresholds abruptos
- Implementa escalação proporcional real
- Evita saltos de 500% por diferenças pequenas
"""

import sys
from pathlib import Path

# Adiciona o diretório do projeto ao path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from utils.config import ConfigManager

def calculate_smooth_volume(zscore, score, beta, base_volume=0.01, max_volume=0.05):
    """
    Calcula volume com progressão suave, sem saltos abruptos
    
    Args:
        zscore: Z-Score absoluto
        score: Score de confiança (0-100)
        beta: Beta absoluto
        base_volume: Volume base
        max_volume: Volume máximo
    
    Returns:
        Volume calculado suavemente
    """
    
    # Normaliza score para 0-1
    score_normalized = max(0, min(100, score)) / 100
    
    # Normaliza zscore para 0-1 (assumindo range 0-4)
    zscore_normalized = max(0, min(4, zscore)) / 4
    
    # Combina fatores com pesos
    combined_factor = (
        score_normalized * 0.6 +      # 60% peso para score
        zscore_normalized * 0.3 +     # 30% peso para zscore  
        min(1.0, beta) * 0.1          # 10% peso para beta (limitado a 1)
    )
    
    # Aplica curva suave (exponencial suave)
    # Evita saltos abruptos usando função suave
    volume_multiplier = 1 + (combined_factor ** 1.5) * 4  # Max 5x
    
    calculated_volume = base_volume * volume_multiplier
    final_volume = min(calculated_volume, max_volume)
    
    return final_volume

def test_smooth_progression():
    """Testa a progressão suave dos volumes"""
    
    print("🔧 TESTE DE PROGRESSÃO SUAVE")
    print("=" * 50)
    
    # Testa com scores variados e zscore fixo
    zscore = 2.15  # Médio dos trades atuais
    beta = 1.0
    
    print("📊 PROGRESSÃO POR SCORE (Z-Score=2.15, Beta=1.0):")
    
    for score in range(55, 75, 2):
        volume = calculate_smooth_volume(zscore, score, beta)
        print(f"   Score {score:2d} → Volume {volume:.4f}")
    
    print(f"\n📈 COMPARAÇÃO COM SISTEMA ATUAL:")
    
    # Casos reais
    cases = [
        ("CHFNOK_CHFDKK", 60.9, 2.147, 0.01, "real"),
        ("AUDSGD_AUDCAD", 63.3, 2.160, 0.05, "real"), 
        ("AUDUSD_AUDCAD", 68.1, 2.133, 0.05, "real")
    ]
    
    for name, score, zscore, volume_real, tipo in cases:
        volume_smooth = calculate_smooth_volume(zscore, score, 1.0)
        
        print(f"\n🔷 {name}:")
        print(f"   Score: {score:.1f}, Z-Score: {zscore:.3f}")
        print(f"   Volume Atual: {volume_real:.4f}")
        print(f"   Volume Suave: {volume_smooth:.4f}")
        print(f"   Diferença: {abs(volume_real - volume_smooth):.4f}")
        
        if abs(volume_real - volume_smooth) < abs(volume_real - 0.01):
            print(f"   ✅ Melhoria: Mais próximo do proporcional")
        else:
            print(f"   📊 Informação: Sistema atual mais conservador")

def demonstrate_problem():
    """Demonstra o problema dos saltos abruptos"""
    
    print("\n🚨 DEMONSTRAÇÃO DO PROBLEMA ATUAL:")
    print("=" * 50)
    
    print("💔 SISTEMA ATUAL (com thresholds):")
    print("   Score 60.9 → Volume 0.01")  
    print("   Score 63.3 → Volume 0.05 (salto de 400%!)")
    print("   Score 68.1 → Volume 0.05")
    print("   Diferença: 2.4 pontos = 400% de aumento!")
    
    print(f"\n💚 SISTEMA CORRIGIDO (progressão suave):")
    
    scores = [60.9, 63.3, 68.1]
    zscore = 2.15
    beta = 1.0
    
    for score in scores:
        volume = calculate_smooth_volume(zscore, score, beta)
        print(f"   Score {score:.1f} → Volume {volume:.4f}")
    
    # Calcula diferenças
    vol1 = calculate_smooth_volume(zscore, 60.9, beta)
    vol2 = calculate_smooth_volume(zscore, 63.3, beta) 
    vol3 = calculate_smooth_volume(zscore, 68.1, beta)
    
    diff_1_2 = ((vol2 - vol1) / vol1) * 100
    diff_2_3 = ((vol3 - vol2) / vol2) * 100
    
    print(f"\n📊 AUMENTOS PROPORCIONAIS:")
    print(f"   60.9 → 63.3: +{diff_1_2:.1f}%")
    print(f"   63.3 → 68.1: +{diff_2_3:.1f}%")
    print(f"   Muito mais suave e proporcional!")

def create_fixed_position_sizer():
    """Cria uma versão corrigida do position sizer"""
    
    code = '''
class PositionSizerFixed:
    """Position Sizer com progressão suave - sem saltos abruptos"""
    
    def __init__(self, config):
        self.config = config
        self.risk_config = config.get_section('risk')
        
    def calculate_volumes_smooth(self, symbol1, symbol2, beta, zscore, score):
        """Calcula volumes com progressão suave"""
        
        base_volume = self.risk_config.get('base_volume', 0.01)
        max_volume = self.risk_config.get('max_volume_per_trade', 0.05)
        
        # Normalização suave
        score_normalized = max(0, min(100, score)) / 100
        zscore_normalized = max(0, min(4, abs(zscore))) / 4
        beta_normalized = min(1.0, abs(beta))
        
        # Combinação com pesos
        combined_factor = (
            score_normalized * 0.6 +
            zscore_normalized * 0.3 +
            beta_normalized * 0.1
        )
        
        # Curva suave (evita saltos)
        volume_multiplier = 1 + (combined_factor ** 1.5) * 4
        
        # Cálculo final
        volume = base_volume * volume_multiplier
        volume = min(volume, max_volume)
        volume = max(volume, base_volume)  # Nunca abaixo do mínimo
        
        return {
            'symbol1_volume': volume,
            'symbol2_volume': volume * abs(beta),
            'method': 'smooth_proportional',
            'multiplier_applied': volume_multiplier,
            'success': True
        }
'''
    
    return code

def main():
    print("🔧 CORREÇÃO DO POSITION SIZING")
    print("🎯 Eliminando escalação desproporcional")
    print("=" * 60)
    
    # Demonstra o problema
    demonstrate_problem()
    
    # Testa progressão suave
    test_smooth_progression()
    
    # Mostra código da correção
    print(f"\n💾 CÓDIGO DA CORREÇÃO DISPONÍVEL")
    print(f"📁 Arquivo: position_sizer_fixed.py")
    
    print(f"\n✅ BENEFÍCIOS DA CORREÇÃO:")
    print(f"   • Remove saltos abruptos de 400%+")
    print(f"   • Progressão suave e proporcional") 
    print(f"   • Diferenças pequenas = aumentos pequenos")
    print(f"   • Mantém gestão de risco adequada")
    print(f"   • Elimina sobre-alavancagem acidental")

if __name__ == "__main__":
    main() 