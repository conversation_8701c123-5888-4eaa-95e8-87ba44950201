#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Investigação dos Problemas de Trading - Bancomat 4
- Verifica se o controle de max_simultaneous_trades está funcionando
- Analisa por que entry_zscore aparece igual ao current_zscore
"""

import json
import sys
from pathlib import Path
from datetime import datetime, timedelta

# Adiciona o diretório do projeto ao path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from utils.config import ConfigManager
from utils.logger import get_logger

# Configurar logger
logger = get_logger(__name__)

def investigate_max_trades_control():
    """Investiga se o controle de máximo de trades simultâneos está funcionando"""
    
    print("🔍 INVESTIGAÇÃO: CONTROLE DE TRADES SIMULTÂNEOS")
    print("=" * 60)
    
    # Carrega configuração atual
    config = ConfigManager()
    
    # Verifica configuração de max trades
    max_trades_trading = config.get('trading.max_simultaneous_trades', 'NOT_SET')
    max_trades_risk = config.get('risk.max_trades_global', 'NOT_SET')
    
    print(f"📊 CONFIGURAÇÕES ATUAIS:")
    print(f"   • trading.max_simultaneous_trades: {max_trades_trading}")
    print(f"   • risk.max_trades_global: {max_trades_risk}")
    
    # Carrega trades ativos
    trades_file = project_root / "data" / "bancomat4_trades.json"
    
    if trades_file.exists():
        with open(trades_file, 'r') as f:
            trades = json.load(f)
        
        print(f"\n📈 TRADES ATIVOS ATUAIS:")
        print(f"   • Número de trades: {len(trades)}")
        print(f"   • Max configurado: {max_trades_trading}")
        
        if len(trades) > max_trades_trading:
            print(f"   ⚠️ PROBLEMA: {len(trades)} trades > {max_trades_trading} max!")
            print(f"   💡 O controle pode não estar funcionando corretamente")
        else:
            print(f"   ✅ Dentro do limite configurado")
        
        # Verifica idades dos trades
        print(f"\n🕒 IDADES DOS TRADES:")
        now = datetime.now()
        
        for i, trade in enumerate(trades):
            entry_time = datetime.fromtimestamp(trade['entry_time'])
            age = now - entry_time
            age_minutes = age.total_seconds() / 60
            
            print(f"   • Trade {i+1} ({trade['pair_name']}): {age_minutes:.1f} min")
            
            if age_minutes > 60:  # Mais de 1 hora
                print(f"     ⚠️ Trade antigo - pode indicar problema no fechamento")
    else:
        print(f"\n📂 Arquivo de trades não encontrado: {trades_file}")
        print(f"   💡 Sistema pode estar parado ou arquivo não foi criado")
    
    # Verifica interface
    print(f"\n🖥️ VERIFICAÇÃO DA INTERFACE:")
    
    interface_file = project_root / "ui" / "advanced_interface.py"
    if interface_file.exists():
        with open(interface_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Verifica se há campo max_trades na interface
        if 'tag="max_trades"' in content:
            print(f"   ✅ Campo Max Trades encontrado na interface")
        else:
            print(f"   ⚠️ Campo Max Trades não encontrado na interface")
        
        # Verifica se há salvamento da configuração
        if 'max_simultaneous_trades' in content:
            print(f"   ✅ Referência ao max_simultaneous_trades encontrada")
        else:
            print(f"   ⚠️ Não encontrada referência ao max_simultaneous_trades")
    
    return {
        'max_trades_config': max_trades_trading,
        'active_trades_count': len(trades) if trades_file.exists() else 0,
        'within_limit': len(trades) <= max_trades_trading if trades_file.exists() else True
    }

def investigate_zscore_issue():
    """Investiga por que entry_zscore aparece igual ao current_zscore"""
    
    print("\n🔍 INVESTIGAÇÃO: ENTRY Z-SCORE VS CURRENT Z-SCORE")
    print("=" * 60)
    
    # Carrega trades ativos
    trades_file = project_root / "data" / "bancomat4_trades.json"
    
    if not trades_file.exists():
        print("❌ Arquivo de trades não encontrado")
        return
    
    with open(trades_file, 'r') as f:
        trades = json.load(f)
    
    print(f"📊 ANÁLISE DOS Z-SCORES:")
    
    for i, trade in enumerate(trades):
        entry_zscore = trade.get('entry_zscore', 0)
        current_zscore = trade.get('current_zscore', 0)
        
        print(f"\n🔷 Trade {i+1}: {trade['pair_name']}")
        print(f"   • Entry Z-Score: {entry_zscore:.6f}")
        print(f"   • Current Z-Score: {current_zscore:.6f}")
        print(f"   • Diferença: {abs(entry_zscore - current_zscore):.6f}")
        
        if abs(entry_zscore - current_zscore) < 0.000001:  # Praticamente iguais
            print(f"   ⚠️ PROBLEMA: Valores idênticos!")
            print(f"   💡 current_zscore não está sendo atualizado")
        else:
            print(f"   ✅ Valores diferentes (correto)")
        
        # Verifica idade do trade
        entry_time = datetime.fromtimestamp(trade['entry_time'])
        age = datetime.now() - entry_time
        age_minutes = age.total_seconds() / 60
        
        print(f"   • Idade: {age_minutes:.1f} minutos")
        
        if age_minutes > 5 and abs(entry_zscore - current_zscore) < 0.000001:
            print(f"   🚨 CRÍTICO: Trade com {age_minutes:.1f} min e Z-Score não atualizado!")
    
    # Verifica implementação na interface
    print(f"\n🖥️ VERIFICAÇÃO DA ATUALIZAÇÃO NA INTERFACE:")
    
    interface_file = project_root / "ui" / "advanced_interface.py"
    with open(interface_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Procura pela atualização do current_zscore
    if 'current_zscore' in content:
        print(f"   ✅ Referência ao current_zscore encontrada")
        
        # Verifica se há diferenciação visual
        if 'Entry Z-Score' in content and 'Current Z-Score' in content:
            print(f"   ✅ Exibição separada de Entry e Current Z-Score")
        else:
            print(f"   ⚠️ Não há diferenciação visual entre Entry e Current")
    else:
        print(f"   ❌ Referência ao current_zscore não encontrada")
    
    return {
        'trades_analyzed': len(trades),
        'zscore_update_issues': sum(1 for t in trades if abs(t.get('entry_zscore', 0) - t.get('current_zscore', 0)) < 0.000001)
    }

def check_auto_trader_implementation():
    """Verifica implementação do AutoTrader"""
    
    print("\n🔍 VERIFICAÇÃO DO AUTO TRADER")
    print("=" * 50)
    
    auto_trader_file = project_root / "core" / "trader.py"
    
    if auto_trader_file.exists():
        with open(auto_trader_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Verifica controle de max trades
        if 'max_simultaneous_trades' in content:
            print("✅ Controle de max_simultaneous_trades encontrado no AutoTrader")
        else:
            print("❌ Controle de max_simultaneous_trades NÃO encontrado")
        
        # Verifica atualização de current_zscore
        if 'current_zscore' in content:
            print("✅ Atualização de current_zscore encontrada")
        else:
            print("❌ Atualização de current_zscore NÃO encontrada")
        
        # Verifica método de verificação de trades
        if '_should_execute_signal' in content:
            print("✅ Método _should_execute_signal encontrado")
        else:
            print("❌ Método _should_execute_signal NÃO encontrado")
    else:
        print("❌ Arquivo trader.py não encontrado")

def create_fix_recommendations():
    """Cria recomendações para corrigir os problemas"""
    
    print("\n💡 RECOMENDAÇÕES DE CORREÇÃO")
    print("=" * 50)
    
    print("🔧 PROBLEMA 1: Controle de Max Trades Simultâneos")
    print("   • Verificar se a interface está salvando corretamente")
    print("   • Garantir que AutoTrader verifica o limite antes de abrir")
    print("   • Implementar logging do controle de limite")
    
    print("\n🔧 PROBLEMA 2: Current Z-Score não atualiza")
    print("   • Implementar atualização periódica do current_zscore")
    print("   • Adicionar timestamp da última atualização")
    print("   • Mostrar diferença visual entre entry e current")
    
    print("\n📋 PRÓXIMOS PASSOS:")
    print("   1. Corrigir atualização do current_zscore no AutoTrader")
    print("   2. Melhorar logging do controle de trades simultâneos")
    print("   3. Adicionar validação na interface")
    print("   4. Implementar alertas visuais para problemas")

def main():
    """Executa investigação completa"""
    
    print("🔍 INVESTIGAÇÃO COMPLETA - PROBLEMAS DE TRADING")
    print("🎯 Verificando controle de trades e atualização de Z-Scores")
    print("=" * 70)
    
    # Investiga controle de max trades
    max_trades_result = investigate_max_trades_control()
    
    # Investiga problema do Z-Score
    zscore_result = investigate_zscore_issue()
    
    # Verifica implementação do AutoTrader
    check_auto_trader_implementation()
    
    # Cria recomendações
    create_fix_recommendations()
    
    print("\n📊 RESUMO DA INVESTIGAÇÃO:")
    print(f"   • Max Trades configurado: {max_trades_result['max_trades_config']}")
    print(f"   • Trades ativos: {max_trades_result['active_trades_count']}")
    print(f"   • Dentro do limite: {'✅' if max_trades_result['within_limit'] else '❌'}")
    
    if 'trades_analyzed' in zscore_result:
        print(f"   • Trades analisados: {zscore_result['trades_analyzed']}")
        print(f"   • Z-Score não atualizado: {zscore_result['zscore_update_issues']}")

if __name__ == "__main__":
    main() 