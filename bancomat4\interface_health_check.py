#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Verificação de Saúde da Interface - Bancomat 4
- Verifica se todos os componentes estão funcionando
- Identifica problemas na inicialização
- Gera relatório de status
"""

import sys
from pathlib import Path

# Adiciona o diretório do projeto ao path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_interface_health():
    """Verifica a saúde da interface"""
    
    print("🏥 VERIFICAÇÃO DE SAÚDE DA INTERFACE")
    print("=" * 50)
    
    try:
        # Testa importações
        print("📦 Testando importações...")
        
        from utils.config import ConfigManager
        print("✅ ConfigManager importado")
        
        from core.mt5_connector import MT5Connector  
        print("✅ MT5Connector importado")
        
        from ui.advanced_interface import AdvancedInterface
        print("✅ AdvancedInterface importado")
        
        # Testa inicialização básica
        print("\n🔧 Testando inicialização...")
        
        config = ConfigManager()
        print("✅ ConfigManager inicializado")
        
        mt5 = MT5Connector(config)
        print("✅ MT5Connector inicializado")
        
        # Testa criação da interface (sem executar)
        interface = AdvancedInterface(config, mt5)
        print("✅ AdvancedInterface criado")
        
        # Verifica atributos essenciais
        print("\n📋 Verificando atributos...")
        
        if hasattr(interface, 'config'):
            print("✅ Atributo config presente")
        else:
            print("❌ Atributo config ausente")
            
        if hasattr(interface, 'mt5'):
            print("✅ Atributo mt5 presente")
        else:
            print("❌ Atributo mt5 ausente")
            
        if hasattr(interface, 'stats'):
            print("✅ Atributo stats presente")
        else:
            print("❌ Atributo stats ausente")
            
        # Verifica métodos críticos
        print("\n🔍 Verificando métodos...")
        
        critical_methods = [
            'create_interface',
            'update_trading_data', 
            'start_auto_trading',
            'initialize_auto_trader'
        ]
        
        for method in critical_methods:
            if hasattr(interface, method):
                print(f"✅ Método {method} presente")
            else:
                print(f"❌ Método {method} ausente")
        
        print("\n✅ VERIFICAÇÃO CONCLUÍDA")
        print("Interface parece estar funcionando corretamente")
        
    except ImportError as e:
        print(f"❌ Erro de importação: {e}")
    except Exception as e:
        print(f"❌ Erro durante verificação: {e}")

if __name__ == "__main__":
    check_interface_health()
