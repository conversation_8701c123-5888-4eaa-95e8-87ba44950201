#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script para limpar sinais duplicados e testar nova lógica
Remove acúmulo de sinais iguais e implementa atualização
"""

import sys
from pathlib import Path
from datetime import datetime

# Adiciona o diretório do projeto ao path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

print("🧹 Limpeza de Sinais Duplicados")
print("=" * 50)

try:
    from utils.config import ConfigManager
    from core.mt5_connector import MT5Connector
    from core.analyzer import PairAnalyzer
    
    # Inicializa componentes
    config = ConfigManager()
    mt5 = MT5Connector(config)
    
    # Conecta MT5
    if not mt5.check_connection():
        if not mt5.connect():
            print("❌ Falha ao conectar MT5")
            sys.exit(1)
    
    print("✅ MT5 conectado")
    
    # Cria analyzer
    analyzer = PairAnalyzer(config, mt5)
    
    # 1. VERIFICA ESTADO ATUAL
    print("\n📊 ESTADO ANTES DA LIMPEZA:")
    current_signals = analyzer.get_active_signals()
    print(f"Total de sinais: {len(current_signals)}")
    
    # Agrupa por par para mostrar duplicatas
    signals_by_pair = {}
    for signal in current_signals:
        if signal.pair_name not in signals_by_pair:
            signals_by_pair[signal.pair_name] = []
        signals_by_pair[signal.pair_name].append(signal)
    
    print(f"Pares únicos: {len(signals_by_pair)}")
    
    for pair_name, signals_list in signals_by_pair.items():
        print(f"  📈 {pair_name}: {len(signals_list)} sinais")
        if len(signals_list) > 1:
            print(f"     ⚠️ DUPLICATAS DETECTADAS!")
            for i, signal in enumerate(signals_list):
                age = (datetime.now() - signal.timestamp).total_seconds()
                print(f"       {i+1}. Z:{signal.z_score:.3f}, Score:{signal.score:.1f}, Idade:{age:.0f}s")
    
    # 2. EXECUTA LIMPEZA FORÇADA
    print("\n🧹 EXECUTANDO LIMPEZA...")
    removed_count = analyzer.force_cleanup_duplicate_signals()
    
    # 3. VERIFICA ESTADO APÓS LIMPEZA
    print("\n📊 ESTADO APÓS LIMPEZA:")
    cleaned_signals = analyzer.get_active_signals()
    print(f"Total de sinais: {len(cleaned_signals)}")
    
    # Reagrupa para verificar resultado
    cleaned_by_pair = {}
    for signal in cleaned_signals:
        if signal.pair_name not in cleaned_by_pair:
            cleaned_by_pair[signal.pair_name] = []
        cleaned_by_pair[signal.pair_name].append(signal)
    
    print(f"Pares únicos: {len(cleaned_by_pair)}")
    
    for pair_name, signals_list in cleaned_by_pair.items():
        print(f"  📈 {pair_name}: {len(signals_list)} sinal(is)")
        for signal in signals_list:
            age = (datetime.now() - signal.timestamp).total_seconds()
            print(f"     Z:{signal.z_score:.3f}, Score:{signal.score:.1f}")
            print(f"     Tipo:{signal.signal_type}, Idade:{age:.0f}s")
            print(f"     Expira:{signal.expires_at.strftime('%H:%M:%S')}")
    
    print("\n✅ LIMPEZA CONCLUÍDA - REINICIE A INTERFACE PARA VER AS MUDANÇAS")
    
except Exception as e:
    print(f"❌ Erro geral: {e}")
    import traceback
    traceback.print_exc() 