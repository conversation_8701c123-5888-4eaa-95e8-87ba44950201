#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Modelo de Signal para Bancomat 4
- Definição da estrutura de um sinal de trading
- Métodos de análise e validação
"""

from dataclasses import dataclass, field
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
import uuid
import json


@dataclass
class SignalCriteria:
    """Critérios de análise de um sinal"""
    
    # Z-Score
    zscore_value: float = 0.0
    zscore_score: float = 0.0
    zscore_valid: bool = False
    
    # Correlação
    correlation_value: float = 0.0
    correlation_score: float = 0.0
    correlation_valid: bool = False
    
    # Cointegração (ADF Test)
    adf_pvalue: float = 1.0
    adf_statistic: float = 0.0
    adf_score: float = 0.0
    adf_valid: bool = False
    
    # Half-life
    half_life_value: float = 0.0
    half_life_score: float = 0.0
    half_life_valid: bool = False
    
    # Cointegração (<PERSON><PERSON>)
    cointegration_pvalue: float = 1.0
    cointegration_score: float = 0.0
    cointegration_valid: bool = False
    
    # Volatilidade do spread
    spread_volatility: float = 0.0
    spread_vol_score: float = 0.0
    spread_vol_valid: bool = False
    
    def get_total_score(self) -> float:
        """Calcula score total baseado nos critérios"""
        weights = {
            'zscore': 0.25,
            'correlation': 0.20,
            'adf': 0.25,
            'half_life': 0.15,
            'cointegration': 0.10,
            'spread_volatility': 0.05
        }
        
        total = (
            self.zscore_score * weights['zscore'] +
            self.correlation_score * weights['correlation'] +
            self.adf_score * weights['adf'] +
            self.half_life_score * weights['half_life'] +
            self.cointegration_score * weights['cointegration'] +
            self.spread_vol_score * weights['spread_volatility']
        )
        
        return min(100.0, max(0.0, total))
    
    def get_valid_criteria_count(self) -> int:
        """Conta quantos critérios são válidos"""
        criteria = [
            self.zscore_valid,
            self.correlation_valid,
            self.adf_valid,
            self.half_life_valid,
            self.cointegration_valid,
            self.spread_vol_valid
        ]
        return sum(criteria)
    
    def meets_minimum_requirements(self, min_criteria: int = 3, min_score: float = 65.0) -> bool:
        """
        Verifica se atende aos requisitos mínimos
        
        Args:
            min_criteria: Número mínimo de critérios válidos
            min_score: Score mínimo
            
        Returns:
            True se atende aos requisitos
        """
        return (self.get_valid_criteria_count() >= min_criteria and 
                self.get_total_score() >= min_score)


@dataclass
class PairAnalysis:
    """Análise completa de um par"""
    
    pair_name: str = ""
    symbol1: str = ""
    symbol2: str = ""
    timeframe: int = 60
    period: int = 200
    
    # Dados estatísticos
    beta: float = 1.0
    current_zscore: float = 0.0
    direction: str = ""  # buy_sell, sell_buy
    
    # Critérios
    criteria: SignalCriteria = field(default_factory=SignalCriteria)
    
    # Dados de mercado
    symbol1_price: float = 0.0
    symbol2_price: float = 0.0
    symbol1_spread: float = 0.0
    symbol2_spread: float = 0.0
    
    # Timestamps
    analyzed_at: datetime = field(default_factory=datetime.now)
    
    def get_signal_strength(self) -> str:
        """
        Determina força do sinal baseado no score
        
        Returns:
            String indicando força (Weak, Medium, Strong, Excellent)
        """
        score = self.criteria.get_total_score()
        
        if score >= 85:
            return "Excellent"
        elif score >= 75:
            return "Strong"
        elif score >= 65:
            return "Medium"
        else:
            return "Weak"
    
    def is_valid_signal(self, min_criteria: int = 3, min_score: float = 65.0) -> bool:
        """
        Verifica se é um sinal válido
        
        Args:
            min_criteria: Número mínimo de critérios válidos
            min_score: Score mínimo
            
        Returns:
            True se é um sinal válido
        """
        return self.criteria.meets_minimum_requirements(min_criteria, min_score)


@dataclass
class Signal:
    """Modelo de um sinal de trading"""
    
    # Identificação
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    pair_name: str = ""
    
    # Análise do par
    pair_analysis: PairAnalysis = field(default_factory=PairAnalysis)
    
    # Estado do sinal
    status: str = "pending"  # pending, executed, expired, cancelled
    priority: int = 0  # 0 = baixa, 100 = alta
    
    # Score e validação
    total_score: float = 0.0
    signal_strength: str = "Weak"
    is_valid: bool = False
    
    # Execução
    execution_time: Optional[datetime] = None
    execution_price1: Optional[float] = None
    execution_price2: Optional[float] = None
    trade_id: Optional[str] = None
    
    # Metadados
    created_at: datetime = field(default_factory=datetime.now)
    expires_at: Optional[datetime] = None
    reason: str = ""
    confidence: float = 0.0
    
    def __post_init__(self):
        """Validações pós-inicialização"""
        if not self.pair_name and self.pair_analysis.pair_name:
            self.pair_name = self.pair_analysis.pair_name
        
        # Calcula score e força do sinal
        self.total_score = self.pair_analysis.criteria.get_total_score()
        self.signal_strength = self.pair_analysis.get_signal_strength()
        self.is_valid = self.pair_analysis.is_valid_signal()
        
        # Define prioridade baseada no score
        self.priority = min(100, int(self.total_score))
        
        # Define expiração se não definida (5 minutos por padrão)
        if self.expires_at is None:
            self.expires_at = self.created_at + timedelta(minutes=5)
        
        # Converte strings para datetime se necessário
        if isinstance(self.created_at, str):
            self.created_at = datetime.fromisoformat(self.created_at.replace('Z', '+00:00'))
        if self.expires_at and isinstance(self.expires_at, str):
            self.expires_at = datetime.fromisoformat(self.expires_at.replace('Z', '+00:00'))
        if self.execution_time and isinstance(self.execution_time, str):
            self.execution_time = datetime.fromisoformat(self.execution_time.replace('Z', '+00:00'))
    
    def is_expired(self) -> bool:
        """
        Verifica se o sinal expirou
        
        Returns:
            True se expirado
        """
        if self.expires_at is None:
            return False
        return datetime.now() > self.expires_at
    
    def time_to_expiry(self) -> Optional[float]:
        """
        Tempo até expiração em segundos
        
        Returns:
            Segundos até expiração ou None se não expira
        """
        if self.expires_at is None:
            return None
        
        remaining = (self.expires_at - datetime.now()).total_seconds()
        return max(0, remaining)
    
    def execute(self, trade_id: str, execution_price1: float, execution_price2: float):
        """
        Marca o sinal como executado
        
        Args:
            trade_id: ID do trade criado
            execution_price1: Preço de execução do símbolo 1
            execution_price2: Preço de execução do símbolo 2
        """
        self.status = "executed"
        self.execution_time = datetime.now()
        self.trade_id = trade_id
        self.execution_price1 = execution_price1
        self.execution_price2 = execution_price2
    
    def cancel(self, reason: str = ""):
        """
        Cancela o sinal
        
        Args:
            reason: Motivo do cancelamento
        """
        self.status = "cancelled"
        self.reason = reason
    
    def expire(self):
        """Marca o sinal como expirado"""
        self.status = "expired"
    
    def get_age_seconds(self) -> float:
        """
        Idade do sinal em segundos
        
        Returns:
            Segundos desde criação
        """
        return (datetime.now() - self.created_at).total_seconds()
    
    def get_age_formatted(self) -> str:
        """
        Idade formatada
        
        Returns:
            String com idade formatada
        """
        age = self.get_age_seconds()
        
        if age < 60:
            return f"{int(age)}s"
        elif age < 3600:
            return f"{int(age // 60)}m {int(age % 60)}s"
        else:
            hours = int(age // 3600)
            minutes = int((age % 3600) // 60)
            return f"{hours}h {minutes}m"
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Converte para dicionário
        
        Returns:
            Dicionário com dados do sinal
        """
        data = {}
        for key, value in self.__dict__.items():
            if isinstance(value, datetime):
                data[key] = value.isoformat()
            elif hasattr(value, 'to_dict'):
                data[key] = value.to_dict()
            elif hasattr(value, '__dict__'):
                # Para dataclasses
                data[key] = {k: v for k, v in value.__dict__.items()}
            else:
                data[key] = value
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Signal':
        """
        Cria instância a partir de dicionário
        
        Args:
            data: Dicionário com dados
            
        Returns:
            Instância de Signal
        """
        # Trata campos de dataclass aninhados
        if 'pair_analysis' in data and isinstance(data['pair_analysis'], dict):
            criteria_data = data['pair_analysis'].get('criteria', {})
            data['pair_analysis']['criteria'] = SignalCriteria(**criteria_data)
            data['pair_analysis'] = PairAnalysis(**data['pair_analysis'])
        
        # Converte timestamps
        for field_name in ['created_at', 'expires_at', 'execution_time']:
            if field_name in data and isinstance(data[field_name], str):
                try:
                    data[field_name] = datetime.fromisoformat(data[field_name].replace('Z', '+00:00'))
                except ValueError:
                    if field_name == 'created_at':
                        data[field_name] = datetime.now()
                    else:
                        data[field_name] = None
        
        return cls(**data)
    
    def __str__(self) -> str:
        """String representation"""
        status_emoji = {
            "pending": "⏳",
            "executed": "✅",
            "expired": "⏰",
            "cancelled": "❌"
        }.get(self.status, "❓")
        
        strength_emoji = {
            "Excellent": "🔥",
            "Strong": "💪",
            "Medium": "👍",
            "Weak": "👎"
        }.get(self.signal_strength, "❓")
        
        return (f"{status_emoji} {self.pair_name} | "
                f"{strength_emoji} {self.signal_strength} ({self.total_score:.1f}) | "
                f"Z-Score: {self.pair_analysis.current_zscore:.2f} | "
                f"Age: {self.get_age_formatted()}")
    
    def __repr__(self) -> str:
        """Representation for debugging"""
        return (f"Signal(id='{self.id[:8]}...', pair='{self.pair_name}', "
                f"score={self.total_score:.1f}, status='{self.status}')")


class SignalManager:
    """Gerenciador de sinais"""
    
    def __init__(self):
        self.signals: List[Signal] = []
    
    def add_signal(self, signal: Signal):
        """Adiciona um novo sinal"""
        self.signals.append(signal)
        self._cleanup_expired()
    
    def get_pending_signals(self) -> List[Signal]:
        """Retorna sinais pendentes válidos"""
        return [s for s in self.signals 
                if s.status == "pending" and s.is_valid and not s.is_expired()]
    
    def get_best_signals(self, count: int = 5) -> List[Signal]:
        """
        Retorna os melhores sinais ordenados por score
        
        Args:
            count: Número máximo de sinais
            
        Returns:
            Lista dos melhores sinais
        """
        pending = self.get_pending_signals()
        return sorted(pending, key=lambda s: s.total_score, reverse=True)[:count]
    
    def get_signal_by_pair(self, pair_name: str) -> Optional[Signal]:
        """
        Busca sinal ativo por nome do par
        
        Args:
            pair_name: Nome do par
            
        Returns:
            Sinal encontrado ou None
        """
        for signal in self.get_pending_signals():
            if signal.pair_name == pair_name:
                return signal
        return None
    
    def execute_signal(self, signal_id: str, trade_id: str, price1: float, price2: float) -> bool:
        """
        Executa um sinal
        
        Args:
            signal_id: ID do sinal
            trade_id: ID do trade criado
            price1: Preço de execução do símbolo 1
            price2: Preço de execução do símbolo 2
            
        Returns:
            True se sucesso
        """
        for signal in self.signals:
            if signal.id == signal_id:
                signal.execute(trade_id, price1, price2)
                return True
        return False
    
    def cancel_signal(self, signal_id: str, reason: str = "") -> bool:
        """
        Cancela um sinal
        
        Args:
            signal_id: ID do sinal
            reason: Motivo do cancelamento
            
        Returns:
            True se sucesso
        """
        for signal in self.signals:
            if signal.id == signal_id:
                signal.cancel(reason)
                return True
        return False
    
    def _cleanup_expired(self):
        """Remove sinais muito antigos"""
        now = datetime.now()
        cutoff = now - timedelta(hours=24)  # Remove sinais de mais de 24h
        
        # Marca expirados
        for signal in self.signals:
            if signal.is_expired() and signal.status == "pending":
                signal.expire()
        
        # Remove sinais antigos
        self.signals = [s for s in self.signals if s.created_at > cutoff]
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Estatísticas dos sinais
        
        Returns:
            Dicionário com estatísticas
        """
        total = len(self.signals)
        pending = len([s for s in self.signals if s.status == "pending"])
        executed = len([s for s in self.signals if s.status == "executed"])
        expired = len([s for s in self.signals if s.status == "expired"])
        cancelled = len([s for s in self.signals if s.status == "cancelled"])
        
        return {
            'total': total,
            'pending': pending,
            'executed': executed,
            'expired': expired,
            'cancelled': cancelled,
            'execution_rate': (executed / total * 100) if total > 0 else 0
        } 