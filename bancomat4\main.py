#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Bancomat 4 - Ponto de entrada principal
- Aplicação de trading automatizado para pares forex
- Interface gráfica moderna com DearPyGui
- Trading contínuo com gestão avançada de risco
"""

import sys
import os
import asyncio
import signal
from pathlib import Path

# Adiciona o diretório do projeto ao path
project_dir = Path(__file__).parent
sys.path.insert(0, str(project_dir))

from utils.logger import setup_logging, get_logger
from utils.config import get_config_manager
from core.mt5_connector import MT5Connector


class BancomatApp:
    """Aplicação principal do Bancomat 4"""
    
    def __init__(self):
        self.config = None
        self.mt5 = None
        self.running = False
        self.logger = None
    
    def setup(self):
        """Configuração inicial da aplicação"""
        try:
            # Configura logging
            print("🔧 Configurando sistema de logging...")
            logging_config = {
                'level': 'INFO',
                'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            }
            setup_logging(logging_config)
            self.logger = get_logger(__name__)
            
            # Carrega configuração
            self.logger.info("📋 Carregando configuração...")
            self.config = get_config_manager()
            
            self.logger.info("✅ Configuração inicial concluída")
            return True
            
        except Exception as e:
            print(f"❌ Erro na configuração inicial: {e}")
            return False
    
    def check_dependencies(self):
        """Verifica dependências essenciais"""
        try:
            self.logger.info("🔍 Verificando dependências...")
            
            # Verifica MetaTrader5
            try:
                import MetaTrader5 as mt5
                self.logger.info("✅ MetaTrader5 disponível")
            except ImportError:
                self.logger.error("❌ MetaTrader5 não encontrado")
                return False
            
            # Verifica pandas
            try:
                import pandas as pd
                self.logger.info("✅ Pandas disponível")
            except ImportError:
                self.logger.error("❌ Pandas não encontrado")
                return False
            
            # Verifica DearPyGui
            try:
                import dearpygui.dearpygui as dpg
                self.logger.info("✅ DearPyGui disponível")
            except ImportError:
                self.logger.error("❌ DearPyGui não encontrado")
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"Erro ao verificar dependências: {e}")
            return False
    
    def setup_mt5(self):
        """Configura conexão MT5"""
        try:
            self.logger.info("🔌 Configurando conexão MT5...")
            
            # Valida caminho do terminal com logs detalhados
            if not self.config.validate_terminal_path():
                terminal_path = self.config.get('mt5.terminal_path')
                self.logger.error(f"❌ Validação do terminal falhou")
                self.logger.error(f"   Terminal configurado: {terminal_path}")
                self.logger.error(f"   Por favor, verifique se o caminho está correto")
                return False
            
            # Cria conector MT5
            self.mt5 = MT5Connector(self.config)
            
            # Tenta conectar
            if self.mt5.connect():
                self.logger.info("✅ Conectado ao MT5 com sucesso")
                
                # Obtém informações da conta
                account_info = self.mt5.get_account_info()
                if account_info:
                    self.logger.info(f"📊 Conta: {account_info.login} | "
                                   f"Saldo: {account_info.balance} {account_info.currency} | "
                                   f"Servidor: {account_info.server}")
                
                # Busca pares forex
                forex_pairs = self.mt5.get_forex_pairs()
                self.logger.info(f"💱 {len(forex_pairs)} pares forex encontrados")
                
                return True
            else:
                self.logger.error("❌ Falha ao conectar ao MT5")
                return False
                
        except Exception as e:
            self.logger.error(f"Erro ao configurar MT5: {e}")
            return False
    
    def run_gui(self):
        """Executa interface gráfica"""
        try:
            self.logger.info("🖥️ Iniciando interface gráfica avançada...")
            
            # Carrega configuração
            self.config = get_config_manager()
            
            # Conecta MT5
            self.mt5 = MT5Connector(self.config)
            if not self.mt5.connect():
                self.logger.error("❌ Falha ao conectar MT5 - interface não pode ser iniciada")
                return False
            
            # Cria interface
            from ui.advanced_interface import AdvancedInterface
            interface = AdvancedInterface(self.config, self.mt5)
            
            # Inicializa sistemas de análise e trading
            self.logger.info("⚙️ Inicializando sistemas de análise e trading...")
            try:
                from core.analyzer import PairAnalyzer
                interface.analyzer = PairAnalyzer(self.config, self.mt5)
                self.logger.info("✅ Sistema de análise inicializado")
            except Exception as e:
                self.logger.warning(f"Sistema de análise não disponível: {e}")
                interface.analyzer = None
            
            try:
                from core.trader import AutoTrader
                if interface.analyzer:
                    interface.auto_trader = AutoTrader(self.config, self.mt5, interface.analyzer)
                    self.logger.info("✅ Sistema de trading automático inicializado")
                else:
                    self.logger.warning("Trading automático não disponível - analyzer necessário")
            except Exception as e:
                self.logger.warning(f"Sistema de trading automático não disponível: {e}")
                interface.auto_trader = None
            
            # Executa interface
            interface.run()
            
            return True
            
        except Exception as e:
            self.logger.error(f"Erro na interface gráfica: {e}")
            import traceback
            self.logger.error(f"Traceback: {traceback.format_exc()}")
            return False
    
    def setup_signal_handlers(self):
        """Configura handlers para sinais do sistema"""
        def signal_handler(signum, frame):
            self.logger.info(f"Sinal {signum} recebido. Encerrando aplicação...")
            self.stop()
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def start(self):
        """Inicia a aplicação"""
        try:
            self.running = True
            
            print("🚀 INICIANDO BANCOMAT 4")
            print("=" * 50)
            
            # Setup inicial
            if not self.setup():
                return False
            
            # Verifica dependências
            if not self.check_dependencies():
                return False
            
            # Configura MT5
            if not self.setup_mt5():
                return False
            
            # Configura signal handlers
            self.setup_signal_handlers()
            
            # Executa interface gráfica
            if not self.run_gui():
                return False
            
            return True
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Erro ao iniciar aplicação: {e}")
            else:
                print(f"❌ Erro ao iniciar aplicação: {e}")
            return False
    
    def stop(self):
        """Para a aplicação"""
        try:
            self.running = False
            
            if self.logger:
                self.logger.info("🛑 Encerrando Bancomat 4...")
            
            # Desconecta MT5
            if self.mt5:
                self.mt5.disconnect()
            
            if self.logger:
                self.logger.info("✅ Bancomat 4 encerrado com sucesso")
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Erro ao encerrar aplicação: {e}")
            else:
                print(f"❌ Erro ao encerrar aplicação: {e}")


def main():
    """Função principal"""
    app = BancomatApp()
    
    try:
        success = app.start()
        return 0 if success else 1
    
    except KeyboardInterrupt:
        print("\n⚠️ Interrompido pelo usuário")
        app.stop()
        return 0
    
    except Exception as e:
        print(f"❌ Erro fatal: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main()) 