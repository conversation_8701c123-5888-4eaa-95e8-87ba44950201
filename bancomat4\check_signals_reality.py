#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script para verificar a realidade dos sinais vs trades
Analisa se os sinais na interface correspondem à situação real
"""

import sys
from pathlib import Path
from datetime import datetime

# Adiciona o diretório do projeto ao path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

print("🔍 Verificação: Sinais vs Realidade")
print("=" * 50)

try:
    from utils.config import ConfigManager
    from core.mt5_connector import MT5Connector
    from core.analyzer import PairAnalyzer
    from core.trader import AutoTrader
    
    # Inicializa componentes
    config = ConfigManager()
    mt5 = MT5Connector(config)
    
    # Conecta MT5
    if not mt5.check_connection():
        if not mt5.connect():
            print("❌ Falha ao conectar MT5")
            sys.exit(1)
    
    print("✅ MT5 conectado")
    
    # Verifica posições abertas no MT5
    print("\n📊 POSIÇÕES REAIS NO MT5:")
    positions = mt5.get_open_positions()
    
    if positions:
        print(f"Total: {len(positions)} posições")
        for i, pos in enumerate(positions, 1):
            print(f"  {i}. {pos['symbol']} {pos['type']} {pos['volume']} - Ticket: {pos['ticket']}")
            print(f"     Preço: {pos['price_open']:.5f} → {pos['price_current']:.5f}")
            print(f"     Lucro: ${pos['profit']:.2f}")
            print(f"     Comentário: {pos.get('comment', 'N/A')}")
    else:
        print("Nenhuma posição aberta")
    
    # Verifica trades no AutoTrader
    print("\n🤖 TRADES NO AUTOTRADER:")
    try:
        auto_trader = AutoTrader(config, mt5, None)
        
        if auto_trader.active_trades:
            print(f"Total: {len(auto_trader.active_trades)} trades ativos")
            for trade_id, trade in auto_trader.active_trades.items():
                print(f"  - ID: {trade_id}")
                print(f"    Par: {trade.pair_name}")
                print(f"    Direção: {trade.direction}")
                print(f"    Tickets: {trade.symbol1_ticket}, {trade.symbol2_ticket}")
                print(f"    Entry: {trade.entry_time}")
                print(f"    Z-Score: {trade.current_zscore:.3f}")
                print(f"    P&L: ${trade.total_profit:.2f}")
        else:
            print("Nenhum trade ativo no AutoTrader")
            
    except Exception as e:
        print(f"❌ Erro ao acessar AutoTrader: {e}")
    
    # Inicializa analyzer para verificar sinais
    print("\n🔬 SINAIS DO ANALYZER:")
    try:
        analyzer = PairAnalyzer(config, mt5)
        
        # Obtém sinais ativos
        active_signals = analyzer.get_active_signals()
        
        if active_signals:
            print(f"Total: {len(active_signals)} sinais ativos")
            
            # Agrupa por par para identificar duplicatas
            signals_by_pair = {}
            for signal in active_signals:
                pair_name = signal.pair_name
                if pair_name not in signals_by_pair:
                    signals_by_pair[pair_name] = []
                signals_by_pair[pair_name].append(signal)
            
            print(f"Pares únicos: {len(signals_by_pair)}")
            
            for pair_name, signals in signals_by_pair.items():
                print(f"\n  📈 {pair_name}:")
                print(f"     Sinais duplicados: {len(signals)}")
                
                latest_signal = max(signals, key=lambda s: s.timestamp)
                print(f"     Último sinal: {latest_signal.signal_type}")
                print(f"     Score: {latest_signal.score:.1f}")
                print(f"     Z-Score: {latest_signal.z_score:.3f}")
                print(f"     Timestamp: {latest_signal.timestamp}")
                print(f"     Expirado: {'Sim' if latest_signal.is_expired() else 'Não'}")
                
                # Verifica se há trade ativo para este par
                has_active_trade = False
                if 'auto_trader' in locals():
                    for trade in auto_trader.active_trades.values():
                        if trade.pair_name == pair_name:
                            has_active_trade = True
                            break
                
                print(f"     Trade ativo: {'Sim' if has_active_trade else 'Não'}")
                
                # Mostra idades dos sinais
                now = datetime.now()
                ages = [(now - s.timestamp).total_seconds() for s in signals]
                print(f"     Idades: {[f'{age:.0f}s' for age in ages]}")
                
        else:
            print("Nenhum sinal ativo")
            
    except Exception as e:
        print(f"❌ Erro ao acessar Analyzer: {e}")
        import traceback
        traceback.print_exc()
    
    # Análise dos problemas
    print("\n" + "=" * 50)
    print("🔍 ANÁLISE DOS PROBLEMAS:")
    
    if 'positions' in locals() and 'auto_trader' in locals():
        mt5_positions = len(positions) if positions else 0
        autotrader_trades = len(auto_trader.active_trades) if hasattr(auto_trader, 'active_trades') else 0
        
        print(f"• Posições MT5: {mt5_positions}")
        print(f"• Trades AutoTrader: {autotrader_trades}")
        
        if mt5_positions != autotrader_trades:
            print("⚠️ INCONSISTÊNCIA: Número de posições MT5 ≠ Trades AutoTrader")
        else:
            print("✅ Sincronização MT5 ↔ AutoTrader está correta")
    
    if 'active_signals' in locals() and 'signals_by_pair' in locals():
        total_signals = len(active_signals)
        unique_pairs = len(signals_by_pair)
        
        print(f"• Total de sinais: {total_signals}")
        print(f"• Pares únicos: {unique_pairs}")
        
        if total_signals > unique_pairs:
            duplicates = total_signals - unique_pairs
            print(f"⚠️ PROBLEMA: {duplicates} sinais duplicados detectados")
            print("   Isso explica por que a GUI mostra mais sinais que trades")
        else:
            print("✅ Não há sinais duplicados")
    
    print("\n📋 RECOMENDAÇÕES:")
    print("1. Limpar sinais expirados e duplicados")
    print("2. Implementar filtro de sinais únicos por par")
    print("3. Sincronizar trades com posições MT5")
    print("4. Atualizar interface para mostrar apenas sinais válidos")
    
except Exception as e:
    print(f"❌ Erro geral: {e}")
    import traceback
    traceback.print_exc() 