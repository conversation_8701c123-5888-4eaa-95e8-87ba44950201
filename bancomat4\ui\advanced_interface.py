#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Interface Gráfica Avançada para Bancomat 4
- Dashboard completo com múltiplas abas
- Visualizações em tempo real
- Controles de trading
- Estatísticas avançadas
"""

import dearpygui.dearpygui as dpg
import time
import threading
from datetime import datetime
from typing import Dict, List, Any, Optional
import platform
from pathlib import Path

from utils.logger import get_logger
from utils.config import ConfigManager
from core.mt5_connector import MT5Connector

logger = get_logger(__name__)


class AdvancedInterface:
    """Interface gráfica avançada para Bancomat 4"""
    
    def __init__(self, config: ConfigManager, mt5: MT5Connector):
        self.config = config
        self.mt5 = mt5
        self.analyzer = None
        self.auto_trader = None
        self.running = False
        
        # Detecta se está no Windows e usa emojis ou texto
        self.is_windows = platform.system() == "Windows"
        
        # Tags dos elementos da interface
        self.tags = {}
        
        # Dados de controle
        self.pairs_analysis_data = []
        self.stats = {
            'total_trades': 0,
            'profitable_trades': 0, 
            'total_profit': 0.0
        }
        
        # Fallback para active_trades como lista vazia
        self.fallback_active_trades = []
        
        self.stats = {
            'total_trades': 0,
            'profitable_trades': 0,
            'active_trades': []
        }
        
        logger.info("🎨 Interface avançada inicializada")
    
    def _format_text(self, text: str) -> str:
        """Formata texto convertendo emojis problemáticos no Windows"""
        if not self.is_windows:
            return text
        
        # Mapeamento de emojis para texto no Windows
        emoji_map = {
            '📊': '[STATS]',
            '🔍': '[SEARCH]',
            '⚙️': '[CONFIG]',
            '✅': '[OK]',
            '❌': '[ERROR]',
            '🔄': '[REFRESH]',
            '🔔': '[SIGNALS]',
            '📋': '[LOGS]',
            '💾': '[SAVE]',
            '🚀': '[START]',
            '🛑': '[STOP]',
            '⚠️': '[WARNING]',
            '💰': '[MONEY]',
            '📈': '[TRADE]',
            '🤖': '[AUTO]',
            '🗑️': '[DELETE]',
            '💱': '[FOREX]',
            '⚡': '[FAST]',
            '🧹': '[CLEAN]',
            '📉': '[DOWN]',
            '💡': '[IDEA]',
            '🔗': '[LINK]',
            '🎨': '[UI]',
            '🔧': '[TOOLS]',
            '📝': '[EDIT]',
            '🎯': '[TARGET]',
            '🏗️': '[BUILD]',
            '👁️': '[VIEW]'
        }
        
        # Substitui emojis por texto
        for emoji, replacement in emoji_map.items():
            text = text.replace(emoji, replacement)
        
        return text
    
    def create_interface(self):
        """Cria a interface completa"""
        try:
            logger.info("🎨 Criando interface avançada...")
            
            # Configurações da janela
            ui_config = self.config.get_section('ui')
            window_width = ui_config.get('window_width', 1600)
            window_height = ui_config.get('window_height', 1000)
            
            logger.info(f"📐 Dimensões da janela: {window_width}x{window_height}")
            
            # Cria contexto DearPyGui primeiro
            logger.info("🔧 Criando contexto DearPyGui...")
            dpg.create_context()
            logger.info("✅ Contexto DearPyGui criado")
            
            # Configurações de viewport
            logger.info("🖼️ Configurando viewport...")
            dpg.create_viewport(
                title="🏦 Bancomat 4 - Expert Advisor Avançado",
                width=window_width,
                height=window_height,
                min_width=1200,
                min_height=800,
                resizable=True
            )
            logger.info("✅ Viewport configurado")
            
            # Temas e cores
            logger.info("🎨 Configurando temas...")
            self.setup_themes()
            logger.info("✅ Temas configurados")
            
            # Janela principal com abas
            logger.info("🏗️ Criando janela principal...")
            with dpg.window(label="Bancomat 4", tag="primary_window"):
                
                # Menu superior
                logger.info("📋 Criando menu...")
                self.create_menu_bar()
                
                # Separador
                dpg.add_separator()
                
                # Abas principais
                logger.info("📑 Criando abas...")
                with dpg.tab_bar(label="Main Tabs"):
                    
                    # Dashboard
                    logger.info("📊 Criando aba Dashboard...")
                    with dpg.tab(label="Dashboard"):
                        self.create_dashboard_tab()
                    
                    # Pares Forex
                    logger.info("💱 Criando aba Pares Forex...")
                    with dpg.tab(label="Pares Forex"):
                        self.create_pairs_tab()
                    
                    # Trading
                    logger.info("📈 Criando aba Trading...")
                    with dpg.tab(label="Trading"):
                        self.create_trading_tab()
                    
                    # Estatísticas
                    logger.info("📊 Criando aba Estatísticas...")
                    with dpg.tab(label="Estatisticas"):
                        self.create_statistics_tab()
                    
                    # Sinais
                    logger.info("🔔 Criando aba Sinais...")
                    with dpg.tab(label="Sinais"):
                        self.create_signals_tab()
                    
                    # Logs
                    logger.info("📋 Criando aba Logs...")
                    with dpg.tab(label="Logs"):
                        self.create_logs_tab()
                    
                    # Configurações
                    logger.info("⚙️ Criando aba Configurações...")
                    with dpg.tab(label="Configuracoes"):
                        self.create_settings_tab()
            
            logger.info("✅ Janela principal criada")
            
            # Configura janela principal
            logger.info("🔧 Configurando janela primária...")
            dpg.set_primary_window("primary_window", True)
            
            # Inicia timer de atualização
            logger.info("⏰ Iniciando timer de atualização...")
            self.start_update_timer()
            
            logger.info("✅ Interface avançada criada com sucesso")
            
        except Exception as e:
            logger.error(f"❌ Erro ao criar interface: {e}")
            import traceback
            logger.error(f"📋 Traceback: {traceback.format_exc()}")
            raise
    
        # Inicializa AutoTrader
        self.initialize_auto_trader()
        

    def setup_themes(self):
        """Configura temas e cores da interface"""
        # Tema escuro moderno
        with dpg.theme() as global_theme:
            with dpg.theme_component(dpg.mvAll):
                dpg.add_theme_color(dpg.mvThemeCol_WindowBg, (30, 30, 30, 255), category=dpg.mvThemeCat_Core)
                dpg.add_theme_color(dpg.mvThemeCol_ChildBg, (40, 40, 40, 255), category=dpg.mvThemeCat_Core)
                dpg.add_theme_color(dpg.mvThemeCol_PopupBg, (35, 35, 35, 255), category=dpg.mvThemeCat_Core)
                dpg.add_theme_color(dpg.mvThemeCol_Text, (220, 220, 220, 255), category=dpg.mvThemeCat_Core)
                dpg.add_theme_color(dpg.mvThemeCol_Button, (60, 60, 60, 255), category=dpg.mvThemeCat_Core)
                dpg.add_theme_color(dpg.mvThemeCol_ButtonHovered, (80, 80, 80, 255), category=dpg.mvThemeCat_Core)
                dpg.add_theme_color(dpg.mvThemeCol_ButtonActive, (100, 100, 100, 255), category=dpg.mvThemeCat_Core)
                dpg.add_theme_color(dpg.mvThemeCol_Header, (70, 70, 70, 255), category=dpg.mvThemeCat_Core)
                dpg.add_theme_color(dpg.mvThemeCol_HeaderHovered, (90, 90, 90, 255), category=dpg.mvThemeCat_Core)
        
        dpg.bind_theme(global_theme)
        
        # Tema para elementos específicos
        with dpg.theme(tag="success_theme"):
            with dpg.theme_component(dpg.mvAll):
                dpg.add_theme_color(dpg.mvThemeCol_Text, (76, 175, 80, 255), category=dpg.mvThemeCat_Core)
        
        with dpg.theme(tag="error_theme"):
            with dpg.theme_component(dpg.mvAll):
                dpg.add_theme_color(dpg.mvThemeCol_Text, (244, 67, 54, 255), category=dpg.mvThemeCat_Core)
        
        with dpg.theme(tag="warning_theme"):
            with dpg.theme_component(dpg.mvAll):
                dpg.add_theme_color(dpg.mvThemeCol_Text, (255, 193, 7, 255), category=dpg.mvThemeCat_Core)
    
    def create_menu_bar(self):
        """Cria barra de menu superior"""
        with dpg.menu_bar():
            with dpg.menu(label="Arquivo"):
                dpg.add_menu_item(label="Salvar Configuração", callback=self.save_config)
                dpg.add_menu_item(label="Carregar Configuração", callback=self.load_config)
                dpg.add_separator()
                dpg.add_menu_item(label="Exportar Logs", callback=self.export_logs)
                dpg.add_separator()
                dpg.add_menu_item(label="Sair", callback=self.quit_app)
            
            with dpg.menu(label="Trading"):
                dpg.add_menu_item(label="🚀 Iniciar Trading", callback=self.start_trading)
                dpg.add_menu_item(label="⏸️ Pausar Trading", callback=self.pause_trading)
                dpg.add_menu_item(label="🛑 Parar Trading", callback=self.stop_trading)
                dpg.add_separator()
                dpg.add_menu_item(label="🔄 Reconectar MT5", callback=self.reconnect_mt5)
            
            with dpg.menu(label="Análise"):
                dpg.add_menu_item(label="🔍 Buscar Pares", callback=self.scan_pairs)
                dpg.add_menu_item(label="📊 Analisar Selecionados", callback=self.analyze_selected)
                dpg.add_menu_item(label="🧮 Calcular Correlações", callback=self.calculate_correlations)
            
            with dpg.menu(label="Ajuda"):
                dpg.add_menu_item(label="📖 Manual", callback=self.show_manual)
                dpg.add_menu_item(label="🏦 Sobre", callback=self.show_about)
    
    def create_dashboard_tab(self):
        """Cria aba do dashboard principal"""
        try:
            logger.info("📊 Iniciando criação do dashboard...")
            
            with dpg.group(horizontal=True):
                
                # Coluna esquerda - Status e informações principais
                with dpg.child_window(width=400, height=600):
                    dpg.add_text("STATUS DO SISTEMA", color=[0, 255, 255])
                    dpg.add_separator()
                    
                    # Status MT5
                    dpg.add_text("Conexao MT5:")
                    self.tags['mt5_status'] = dpg.add_text("Verificando...", color=[255, 255, 0])
                    
                    dpg.add_spacer(height=10)
                    
                    # Informações da conta
                    dpg.add_text("INFORMACOES DA CONTA", color=[0, 255, 255])
                    dpg.add_separator()
                    
                    self.tags['account_login'] = dpg.add_text("Conta: ---")
                    self.tags['account_balance'] = dpg.add_text("Saldo: ---")
                    self.tags['account_equity'] = dpg.add_text("Equity: ---")
                    self.tags['account_margin'] = dpg.add_text("Margem: ---")
                    self.tags['account_free_margin'] = dpg.add_text("Margem Livre: ---")
                    self.tags['account_margin_level'] = dpg.add_text("Nivel de Margem: ---")
                    
                    dpg.add_spacer(height=10)
                    
                    # Resumo de trading
                    dpg.add_text("RESUMO DE TRADING", color=[0, 255, 255])
                    dpg.add_separator()
                    
                    self.tags['total_trades'] = dpg.add_text("Total de Trades: 0")
                    self.tags['active_trades'] = dpg.add_text("Trades Ativos: 0")
                    self.tags['profitable_trades'] = dpg.add_text("Trades Lucrativos: 0 (0%)")
                    self.tags['total_profit'] = dpg.add_text("Lucro Total: $0.00")
                    self.tags['current_drawdown'] = dpg.add_text("Drawdown: 0%")
                    
                    dpg.add_spacer(height=20)
                    
                    # Controles principais
                    dpg.add_text("CONTROLES", color=[0, 255, 255])
                    dpg.add_separator()
                    
                    with dpg.group(horizontal=True):
                        dpg.add_button(label="Iniciar", callback=self.start_trading, width=80)
                        dpg.add_button(label="Pausar", callback=self.pause_trading, width=80)
                        dpg.add_button(label="Parar", callback=self.stop_trading, width=80)
                    
                    dpg.add_spacer(height=10)
                    dpg.add_button(label="Reconectar MT5", callback=self.reconnect_mt5, width=250)
                    dpg.add_button(label="Buscar Pares", callback=self.scan_pairs, width=250)
                
                # Coluna direita - Informações simplificadas
                with dpg.child_window(width=-1, height=600):
                    dpg.add_text("MONITORAMENTO EM TEMPO REAL", color=[0, 255, 255])
                    dpg.add_separator()
                    
                    # Informações básicas
                    dpg.add_text("Estatisticas Rapidas")
                    dpg.add_separator()
                    
                    self.tags['quick_stats'] = dpg.add_text("Sistema iniciado com sucesso")
                    dpg.add_text("Interface funcionando corretamente")
                    dpg.add_text("Conexao MT5 estabelecida")
                    
                    dpg.add_spacer(height=20)
                    
                    # Lista simplificada de pares ativos
                    dpg.add_text("PARES DISPONIVEIS", color=[255, 255, 0])
                    dpg.add_separator()
                    
                    # Criar tabela simples primeiro para testar
                    try:
                        with dpg.table(header_row=True, tag="active_pairs_table"):
                            dpg.add_table_column(label="Par")
                            dpg.add_table_column(label="Status")
                            dpg.add_table_column(label="Preco")
                    except Exception as e:
                        logger.error(f"Erro ao criar tabela: {e}")
                        dpg.add_text("Tabela de pares sera carregada...")
                    
                    dpg.add_spacer(height=20)
                    
                    # Status simplificado
                    dpg.add_text("STATUS ATUAL", color=[255, 255, 0])
                    dpg.add_separator()
                    
                    self.tags['system_uptime'] = dpg.add_text("Sistema: Online")
                    self.tags['pairs_count'] = dpg.add_text("Pares: Carregando...")
                    self.tags['last_check'] = dpg.add_text("Ultima verificacao: ---")
                
            # Linha inferior - Status e informações extras
            dpg.add_spacer(height=10)
            with dpg.group(horizontal=True):
                dpg.add_text("Ultima Atualizacao:")
                self.tags['last_update'] = dpg.add_text("---")
                dpg.add_spacer(width=50)
                dpg.add_text("Pares Disponiveis:")
                self.tags['available_pairs'] = dpg.add_text("---")
                dpg.add_spacer(width=50)
                dpg.add_text("Uptime:")
                self.tags['uptime'] = dpg.add_text("---")
            
            logger.info("✅ Dashboard criado com sucesso")
            
        except Exception as e:
            logger.error(f"❌ Erro ao criar dashboard: {e}")
            import traceback
            logger.error(f"📋 Traceback: {traceback.format_exc()}")
            
            # Cria dashboard simplificado em caso de erro
            dpg.add_text("🏠 DASHBOARD SIMPLIFICADO", color=[255, 255, 0])
            dpg.add_text("Interface carregada com sucesso!")
            dpg.add_text("Conectado ao MT5")
            dpg.add_button(label="🔄 Tentar Novamente", callback=self.reconnect_mt5)
    
    def create_pairs_tab(self):
        """Cria aba de pares forex com análise detalhada"""
        try:
            logger.info("💱 Criando aba de pares...")
            
            with dpg.group(horizontal=True):
                
                # Painel de controle esquerdo
                with dpg.child_window(width=300, height=600):
                    dpg.add_text("CONTROLES DE ANÁLISE", color=[0, 255, 255])
                    dpg.add_separator()
                    
                    # Estatísticas rápidas
                    dpg.add_text("ESTATÍSTICAS")
                    dpg.add_separator()
                    
                    self.tags['pairs_total'] = dpg.add_text("Total de pares: 0")
                    self.tags['pairs_analyzed'] = dpg.add_text("Analisados: 0")
                    self.tags['pairs_approved'] = dpg.add_text("Aprovados: 0")
                    self.tags['pairs_with_signals'] = dpg.add_text("Com sinais: 0")
                    self.tags['pairs_best_score'] = dpg.add_text("Melhor score: 0.0")
                    
                    dpg.add_separator()
                    
                    # Ações
                    dpg.add_text("AÇÕES")
                    dpg.add_separator()
                    
                    dpg.add_button(label="🔄 Atualizar Análise", callback=self.update_pairs_analysis_data, width=-1)
                    dpg.add_button(label="⚡ Forçar Análise Completa", callback=self.force_complete_analysis, width=-1)
                    dpg.add_button(label="🔍 Analisar Par Selecionado", callback=self.analyze_selected_pair, width=-1)
                    
                    dpg.add_separator()
                    
                    # Filtros
                    dpg.add_text("FILTROS")
                    dpg.add_separator()
                    
                    self.tags['filter_approved_only'] = dpg.add_checkbox(label="Mostrar apenas aprovados", default_value=False, callback=self.apply_pairs_filter)
                    self.tags['filter_with_signals'] = dpg.add_checkbox(label="Mostrar apenas com sinais", default_value=False, callback=self.apply_pairs_filter)
                    
                    dpg.add_text("Score mínimo")
                    self.tags['filter_min_score'] = dpg.add_slider_float(
                        label="", 
                        default_value=0.0, 
                        min_value=0.0, 
                        max_value=100.0, 
                        format="%.1f",
                        callback=self.apply_pairs_filter,
                        width=-50
                    )
                    
                    dpg.add_separator()
                    
                    # Status da análise
                    dpg.add_text("STATUS DA ANÁLISE")
                    dpg.add_separator()
                    
                    self.tags['pairs_analysis_status'] = dpg.add_text("📊 20 pares analisados")
                    self.tags['pairs_last_update'] = dpg.add_text("Última atualização: 10:07:05")
                    
                    dpg.add_spacer(height=20)
                    
                    # Informações do par selecionado
                    dpg.add_text("PAR SELECIONADO")
                    dpg.add_separator()
                    
                    self.tags['selected_pair_details'] = dpg.add_text("Selecione um par da tabela", wrap=280)
                
                # Tabela principal de pares
                with dpg.child_window(width=-1, height=600):
                    dpg.add_text("ANÁLISE COMPLETA DE PARES", color=[0, 255, 255])
                    dpg.add_separator()
                    
                    # Controles da tabela
                    with dpg.group(horizontal=True):
                        dpg.add_text("Ordenar por:")
                        dpg.add_combo(["Score (Maior)", "Score (Menor)", "Nome A-Z", "Status"], 
                                     default_value="Score (Maior)", 
                                     tag="pairs_sort_combo",
                                     callback=self.update_pairs_table,
                                     width=150)
                        
                        dpg.add_spacer(width=20)
                        dpg.add_button(label="📋 Exportar Tabela", callback=self.export_pairs_table, width=120)
                    
                    dpg.add_spacer(height=10)
                    
                    # Tabela detalhada de pares
                    try:
                        with dpg.table(header_row=True, tag="pairs_analysis_table", width=-1, height=500,
                                     sortable=True, resizable=True, borders_innerH=True, borders_outerH=True,
                                     borders_innerV=True, borders_outerV=True, row_background=True):
                            
                            # Colunas da tabela
                            dpg.add_table_column(label="Par", width_fixed=True, init_width_or_weight=80)
                            dpg.add_table_column(label="Score", width_fixed=True, init_width_or_weight=60)
                            dpg.add_table_column(label="Status", width_fixed=True, init_width_or_weight=100)
                            dpg.add_table_column(label="Z-Score", width_fixed=True, init_width_or_weight=70)
                            dpg.add_table_column(label="Correlação", width_fixed=True, init_width_or_weight=80)
                            dpg.add_table_column(label="Cointegração", width_fixed=True, init_width_or_weight=90)
                            dpg.add_table_column(label="Half-Life", width_fixed=True, init_width_or_weight=80)
                            dpg.add_table_column(label="Beta", width_fixed=True, init_width_or_weight=70)
                            dpg.add_table_column(label="Confiança", width_fixed=True, init_width_or_weight=80)
                            dpg.add_table_column(label="Última Análise", width_fixed=True, init_width_or_weight=120)
                    
                    except Exception as e:
                        logger.error(f"Erro ao criar tabela de análise: {e}")
                        dpg.add_text("Tabela de análise será carregada...")
            
            # Inicia atualização automática
            self.pairs_analysis_data = []  # Cache dos dados de análise
            self.update_pairs_analysis_data()
            
            logger.info("✅ Aba de pares criada")
            
        except Exception as e:
            logger.error(f"❌ Erro ao criar aba de pares: {e}")
            dpg.add_text("Erro ao carregar aba de pares")
    
    def create_trading_tab(self):
        """Cria aba de trading"""
        try:
            logger.info("📈 Criando aba de trading...")
            
            with dpg.group(horizontal=True):
                
                # Painel de controle
                with dpg.child_window(width=350, height=600):
                    dpg.add_text("CONTROLE DE TRADING", color=[0, 255, 255])
                    dpg.add_separator()
                    
                    # Status atual
                    dpg.add_text("STATUS ATUAL")
                    dpg.add_separator()
                    
                    self.tags['trading_status'] = dpg.add_text("Status: Parado", color=[255, 255, 0])
                    self.tags['trading_mode'] = dpg.add_text("Modo: Manual")
                    self.tags['open_positions'] = dpg.add_text("Posicoes Abertas: 0")
                    self.tags['pending_signals'] = dpg.add_text("Sinais Pendentes: 0")
                    
                    dpg.add_spacer(height=20)
                    
                    # Controles principais
                    dpg.add_text("CONTROLES")
                    dpg.add_separator()
                    
                    dpg.add_button(label="Iniciar Trading", callback=self.start_auto_trading, width=300)
                    dpg.add_button(label="Pausar Trading", callback=self.pause_trading, width=300)
                    dpg.add_button(label="Parar Trading", callback=self.stop_and_close_all, width=300)
                    
                    dpg.add_spacer(height=20)
                    
                    # Configurações básicas
                    dpg.add_text("CONFIGURACOES BASICAS")
                    dpg.add_separator()
                    
                    dpg.add_input_int(label="Max Trades", default_value=5, tag="max_trades", width=200, callback=self.save_max_trades_config)
                    dpg.add_input_float(label="Risco por Trade (%)", default_value=1.0, tag="risk_per_trade", width=200)
                    dpg.add_input_float(label="Z-Score Minimo", default_value=2.0, tag="min_zscore", width=200)
                
                # Lista de trades (simplificada)
                with dpg.child_window(width=-1, height=600):
                    dpg.add_text("TRADES ATIVOS", color=[0, 255, 255])
                    dpg.add_separator()
                    
                    dpg.add_text("Nenhum trade ativo no momento")
                    
                    dpg.add_spacer(height=20)
                    
                    # Controles da tabela
                    with dpg.group(horizontal=True):
                        dpg.add_button(label="Atualizar", callback=self.refresh_trades, width=100)
                        dpg.add_button(label="Estatisticas", callback=self.show_trade_stats, width=100)
                    
                    dpg.add_spacer(height=20)
                    
                    # Lista de trades (será implementada depois)
                    self.tags['trades_info'] = dpg.add_text("Lista de trades sera carregada aqui...")
            
            logger.info("✅ Aba de trading criada")
            
        except Exception as e:
            logger.error(f"❌ Erro ao criar aba de trading: {e}")
            dpg.add_text("Erro ao carregar trading")
    
    def create_statistics_tab(self):
        """Cria aba de estatísticas"""
        try:
            logger.info("📊 Criando aba de estatísticas...")
            
            dpg.add_text("📊 ESTATÍSTICAS", color=[0, 255, 255])
            dpg.add_separator()
            
            dpg.add_text("Estatísticas serão exibidas aqui...")
            dpg.add_button(label="📊 Gerar Relatório", callback=self.generate_report)
            
            logger.info("✅ Aba de estatísticas criada")
            
        except Exception as e:
            logger.error(f"❌ Erro ao criar aba de estatísticas: {e}")
            dpg.add_text("❌ Erro ao carregar estatísticas")
    
    def create_signals_tab(self):
        """Cria aba de sinais"""
        try:
            logger.info("🔔 Criando aba de sinais...")
            
            with dpg.group(horizontal=True):
                
                # Lista de sinais
                with dpg.child_window(width=450, height=600):
                    dpg.add_text("SINAIS DE TRADING", color=[0, 255, 255])
                    dpg.add_separator()
                    
                    # Controles
                    with dpg.group(horizontal=True):
                        dpg.add_button(label="🔍 Buscar", callback=self.scan_signals, width=80)
                        dpg.add_button(label="🔄 Atualizar", callback=self.refresh_signals, width=80)
                        dpg.add_button(label="🧹 Limpar", callback=self.clear_expired_signals, width=80)
                    
                    dpg.add_spacer(height=10)
                    
                    # Status dos sinais
                    dpg.add_text("STATUS DOS SINAIS")
                    dpg.add_separator()
                    
                    self.tags['signals_count'] = dpg.add_text("Sinais ativos: 0")
                    self.tags['signals_last_scan'] = dpg.add_text("Ultimo scan: ---")
                    
                    dpg.add_spacer(height=10)
                    
                    # Lista de sinais
                    dpg.add_text("LISTA DE SINAIS ATIVOS")
                    dpg.add_separator()
                    
                    try:
                        with dpg.table(header_row=True, tag="signals_table", width=430):
                            dpg.add_table_column(label="Par", width=80)
                            dpg.add_table_column(label="Tipo", width=40)
                            dpg.add_table_column(label="Z-Score", width=50)
                            dpg.add_table_column(label="Score", width=40)
                            dpg.add_table_column(label="Vol1", width=40)
                            dpg.add_table_column(label="Vol2", width=40)
                            dpg.add_table_column(label="Expira", width=60)
                    except Exception as e:
                        logger.error(f"Erro ao criar tabela de sinais: {e}")
                        dpg.add_text("Tabela de sinais sera carregada...")
                
                # Detalhes do sinal selecionado
                with dpg.child_window(width=-1, height=600):
                    dpg.add_text("DETALHES DO SINAL", color=[0, 255, 255])
                    dpg.add_separator()
                    
                    self.tags['signal_details'] = dpg.add_text("Selecione um sinal da lista ou aguarde novos sinais")
                    
                    dpg.add_spacer(height=20)
                    
                    # Critérios do sinal
                    dpg.add_text("CRITERIOS APROVADOS")
                    dpg.add_separator()
                    
                    self.tags['signal_criteria'] = dpg.add_text("Nenhum sinal selecionado")
                    
                    dpg.add_spacer(height=20)
                    
                    # Ações do sinal
                    dpg.add_text("ACOES DISPONIVEIS")
                    dpg.add_separator()
                    
                    with dpg.group(horizontal=True):
                        dpg.add_button(label="📈 Executar Agora", callback=self.execute_signal, width=120)
                        dpg.add_button(label="🤖 Modo Automático", callback=self.schedule_signal, width=120)
                        dpg.add_button(label="🗑️ Remover Sinal", callback=self.ignore_signal, width=120)
                    
                    dpg.add_spacer(height=10)
                    
                    # Botões adicionais
                    with dpg.group(horizontal=True):
                        dpg.add_button(label="📊 Analisar Detalhado", callback=self.analyze_selected_signal, width=120)
                        dpg.add_button(label="📋 Copiar Informações", callback=self.copy_signal_info, width=120)
                        dpg.add_button(label="⚠️ Alertas", callback=self.configure_signal_alerts, width=120)
                    
                    dpg.add_spacer(height=20)
                    
                    # Configurações de execução
                    dpg.add_text("CONFIGURACOES DE EXECUCAO")
                    dpg.add_separator()
                    
                    dpg.add_input_float(label="Volume Customizado", default_value=0.01, tag="signal_volume", width=150)
                    dpg.add_input_float(label="Risk % Customizado", default_value=1.0, tag="signal_risk", width=150)
                    dpg.add_checkbox(label="Auto-fechar em reversao", default_value=True, tag="signal_auto_close")
                    dpg.add_checkbox(label="Confirmar antes de executar", default_value=False, tag="signal_confirm")
                    
                    dpg.add_spacer(height=20)
                    
                    # Status da execução
                    dpg.add_text("STATUS DA EXECUCAO")
                    dpg.add_separator()
                    
                    self.tags['signal_execution_status'] = dpg.add_text("Aguardando ação do usuário")
            
            logger.info("✅ Aba de sinais criada")
            
        except Exception as e:
            logger.error(f"❌ Erro ao criar aba de sinais: {e}")
            dpg.add_text("❌ Erro ao carregar sinais")
    
    def create_logs_tab(self):
        """Cria aba de logs"""
        try:
            logger.info("📋 Criando aba de logs...")
            
            dpg.add_text("📋 LOGS DO SISTEMA", color=[0, 255, 255])
            dpg.add_separator()
            
            # Área de texto dos logs simplificada
            self.tags['logs_text'] = dpg.add_input_text(
                multiline=True,
                readonly=True,
                height=500,
                width=-1,
                default_value="Sistema iniciado...\nConexão MT5 estabelecida...\nInterface carregada...\n"
            )
            
            dpg.add_button(label="🔄 Atualizar Logs", callback=self.refresh_logs)
            
            logger.info("✅ Aba de logs criada")
            
        except Exception as e:
            logger.error(f"❌ Erro ao criar aba de logs: {e}")
            dpg.add_text("❌ Erro ao carregar logs")
    
    def create_settings_tab(self):
        """Cria aba de configurações"""
        try:
            logger.info("⚙️ Criando aba de configurações...")
            
            dpg.add_text("⚙️ CONFIGURAÇÕES", color=[0, 255, 255])
            dpg.add_separator()
            
            # Configurações básicas
            dpg.add_text("Configurações básicas:")
            dpg.add_input_text(label="Terminal MT5", default_value=self.config.get('mt5.terminal_path', ''), width=400)
            dpg.add_input_int(label="Max Trades", default_value=5, width=200)
            
            dpg.add_spacer(height=20)
            dpg.add_button(label="💾 Salvar", callback=self.save_settings)
            
            logger.info("✅ Aba de configurações criada")
            
        except Exception as e:
            logger.error(f"❌ Erro ao criar aba de configurações: {e}")
            dpg.add_text("❌ Erro ao carregar configurações")
    
    def start_update_timer(self):
        """Inicia timer de atualização da interface"""
        self.running = True
        
        def update_loop():
            while self.running:
                try:
                    self.update_interface()
                    time.sleep(1)  # Atualiza a cada segundo
                except Exception as e:
                    logger.error(f"Erro no loop de atualização: {e}")
                    time.sleep(5)  # Espera mais em caso de erro
        
        update_thread = threading.Thread(target=update_loop, daemon=True)
        update_thread.start()
    
    def update_interface(self):
        """Atualiza todos os elementos da interface"""
        try:
            # Atualiza status MT5
            self.update_mt5_status()
            
            # Atualiza informações da conta
            self.update_account_info()
            
            # Atualiza dados de trading
            self.update_trading_data()
            
            # Atualiza sinais (se analisador disponível)
            if self.analyzer:
                self.update_signals_data()
                
                # Atualiza dados de análise dos pares (a cada 10 segundos para não sobrecarregar)
                current_time = time.time()
                if not hasattr(self, '_last_pairs_update') or (current_time - self._last_pairs_update) >= 10:
                    self.update_pairs_analysis_data()
                    self._last_pairs_update = current_time
            
            # Atualiza timestamp
            current_time = datetime.now().strftime("%H:%M:%S")
            if "last_update" in self.tags:
                dpg.set_value(self.tags["last_update"], current_time)
                
        except Exception as e:
            logger.error(f"Erro ao atualizar interface: {e}")
    
    def update_mt5_status(self):
        """Atualiza status de conexão MT5"""
        try:
            if self.mt5 and self.mt5.check_connection():
                status_text = "✅ Conectado"
                color = [0, 255, 0]
            else:
                status_text = "❌ Desconectado"
                color = [255, 0, 0]
            
            if "mt5_status" in self.tags:
                dpg.set_value(self.tags["mt5_status"], status_text)
                dpg.bind_item_theme(self.tags["mt5_status"], 
                                   "success_theme" if color == [0, 255, 0] else "error_theme")
                
        except Exception as e:
            logger.error(f"Erro ao atualizar status MT5: {e}")
    
    def update_account_info(self):
        """Atualiza informações da conta"""
        try:
            if not self.mt5 or not self.mt5.check_connection():
                return
            
            account_info = self.mt5.get_account_info()
            if account_info:
                # Atualiza labels de conta
                if "account_login" in self.tags:
                    dpg.set_value(self.tags["account_login"], f"Conta: {account_info.login}")
                if "account_balance" in self.tags:
                    dpg.set_value(self.tags["account_balance"], f"Saldo: {account_info.balance:.2f} {account_info.currency}")
                if "account_equity" in self.tags:
                    dpg.set_value(self.tags["account_equity"], f"Equity: {account_info.equity:.2f} {account_info.currency}")
                if "account_margin" in self.tags:
                    dpg.set_value(self.tags["account_margin"], f"Margem: {account_info.margin:.2f} {account_info.currency}")
                if "account_free_margin" in self.tags:
                    dpg.set_value(self.tags["account_free_margin"], f"Margem Livre: {account_info.margin_free:.2f} {account_info.currency}")
                if "account_margin_level" in self.tags:
                    if account_info.margin_level > 0:
                        dpg.set_value(self.tags["account_margin_level"], f"Nivel de Margem: {account_info.margin_level:.2f}%")
                    else:
                        dpg.set_value(self.tags["account_margin_level"], "Nivel de Margem: N/A")
                
                # Atualiza pares disponíveis
                forex_pairs = self.mt5.get_forex_pairs()
                if "available_pairs" in self.tags:
                    dpg.set_value(self.tags["available_pairs"], str(len(forex_pairs)))
                
                # Atualiza informações de sistema
                if "system_uptime" in self.tags:
                    dpg.set_value(self.tags["system_uptime"], "Sistema: Online")
                if "pairs_count" in self.tags:
                    dpg.set_value(self.tags["pairs_count"], f"Pares: {len(forex_pairs)}")
                if "last_check" in self.tags:
                    dpg.set_value(self.tags["last_check"], f"Ultima verificacao: {datetime.now().strftime('%H:%M:%S')}")
                    
        except Exception as e:
            logger.error(f"Erro ao atualizar informações da conta: {e}")
    
    def update_trading_data(self):
        """Atualiza dados de trading"""
        try:
            # Obtém dados do auto trader se disponível
            if self.auto_trader:
                active_trades = self.auto_trader.get_active_trades()
                stats = self.auto_trader.get_stats()
                
                # CORREÇÃO: Verifica se stats é válido
                if not stats or not isinstance(stats, dict):
                    logger.warning("⚠️ Stats do AutoTrader inválido, usando fallback")
                    stats = {
                        'total_trades': 0,
                        'profitable_trades': 0,
                        'total_profit': 0.0,
                        'win_rate': 0.0
                    }
                
                # Garante que todas as chaves necessárias existem
                required_keys = ['total_trades', 'profitable_trades', 'total_profit', 'win_rate']
                for key in required_keys:
                    if key not in stats:
                        stats[key] = 0.0 if 'profit' in key or 'rate' in key else 0
                
                # Atualiza contadores na aba Trading
                if "trading_status" in self.tags:
                    if hasattr(self.auto_trader, 'running') and self.auto_trader.running:
                        dpg.set_value(self.tags["trading_status"], "Status: Trading Automático")
                        if "trading_mode" in self.tags:
                            dpg.set_value(self.tags["trading_mode"], "Modo: Automático")
                    else:
                        dpg.set_value(self.tags["trading_status"], "Status: Parado")
                        if "trading_mode" in self.tags:
                            dpg.set_value(self.tags["trading_mode"], "Modo: Manual")
                
                # Atualiza posições abertas
                if "open_positions" in self.tags:
                    positions_count = len(active_trades) if active_trades else 0
                    dpg.set_value(self.tags["open_positions"], f"Posições Abertas: {positions_count}")
                
                # Atualiza sinais pendentes
                if "pending_signals" in self.tags and self.analyzer:
                    active_signals = self.analyzer.get_active_signals()
                    signals_count = len(active_signals) if active_signals else 0
                    dpg.set_value(self.tags["pending_signals"], f"Sinais Pendentes: {signals_count}")
                
                # Atualiza stats do dashboard com verificações seguras
                if "total_trades" in self.tags:
                    dpg.set_value(self.tags["total_trades"], f"Total de Trades: {stats.get('total_trades', 0)}")
                if "active_trades" in self.tags:
                    active_count = len(active_trades) if active_trades else 0
                    dpg.set_value(self.tags["active_trades"], f"Trades Ativos: {active_count}")
                if "profitable_trades" in self.tags:
                    win_rate = stats.get('win_rate', 0)
                    profitable = stats.get('profitable_trades', 0)
                    dpg.set_value(self.tags["profitable_trades"], f"Trades Lucrativos: {profitable} ({win_rate:.1f}%)")
                if "total_profit" in self.tags:
                    total_profit = stats.get('total_profit', 0.0)
                    dpg.set_value(self.tags["total_profit"], f"Lucro Total: ${total_profit:.2f}")
                
                # Atualiza informações detalhadas dos trades
                if "trades_info" in self.tags and active_trades:
                    trades_text = f"TRADES ATIVOS ({len(active_trades)}):\n\n"
                    
                    for trade in active_trades:
                        try:
                            age_minutes = (datetime.now() - trade.entry_time).total_seconds() / 60
                            trades_text += f"• {trade.pair_name}\n"
                            trades_text += f"  ID: {trade.id}\n"
                            trades_text += f"  Direção: {trade.direction}\n"
                            trades_text += f"  Entry Z-Score: {trade.entry_zscore:.3f}\n"
                            trades_text += f"  Current Z-Score: {trade.current_zscore:.3f}\n"
                            trades_text += f"  P&L: ${getattr(trade, 'total_profit', 0.0):.2f}\n"
                            trades_text += f"  Idade: {age_minutes:.1f} min\n"
                            trades_text += f"  Score: {trade.signal_score:.1f}\n\n"
                        except Exception as e:
                            logger.warning(f"Erro ao processar trade {trade.id}: {e}")
                            trades_text += f"• {trade.pair_name} (erro nos dados)\n\n"
                    
                    dpg.set_value(self.tags["trades_info"], trades_text)
                elif "trades_info" in self.tags:
                    dpg.set_value(self.tags["trades_info"], "Nenhum trade ativo no momento")
            else:
                # Dados padrão quando AutoTrader não disponível
                if "trading_status" in self.tags:
                    dpg.set_value(self.tags["trading_status"], "Status: Sistema Parado")
                if "trading_mode" in self.tags:
                    dpg.set_value(self.tags["trading_mode"], "Modo: Manual")
                if "open_positions" in self.tags:
                    dpg.set_value(self.tags["open_positions"], "Posições Abertas: 0")
                if "pending_signals" in self.tags and self.analyzer:
                    active_signals = self.analyzer.get_active_signals()
                    signals_count = len(active_signals) if active_signals else 0
                    dpg.set_value(self.tags["pending_signals"], f"Sinais Pendentes: {signals_count}")
                elif "pending_signals" in self.tags:
                    dpg.set_value(self.tags["pending_signals"], "Sinais Pendentes: 0")
                
                # Stats padrão do dashboard com fallback seguro
                if "total_trades" in self.tags:
                    total_trades = self.stats.get('total_trades', 0) if hasattr(self, 'stats') else 0
                    dpg.set_value(self.tags["total_trades"], f"Total de Trades: {total_trades}")
                if "active_trades" in self.tags:
                    dpg.set_value(self.tags["active_trades"], f"Trades Ativos: 0")
                if "profitable_trades" in self.tags:
                    profitable = self.stats.get('profitable_trades', 0) if hasattr(self, 'stats') else 0
                    dpg.set_value(self.tags["profitable_trades"], f"Trades Lucrativos: {profitable} (0%)")
                if "total_profit" in self.tags:
                    total_profit = self.stats.get('total_profit', 0.0) if hasattr(self, 'stats') else 0.0
                    dpg.set_value(self.tags["total_profit"], f"Lucro Total: ${total_profit:.2f}")
                
        except Exception as e:
            logger.error(f"Erro ao atualizar dados de trading: {e}")
            # Log do tipo de erro para debug
            import traceback
            logger.debug(f"Stack trace: {traceback.format_exc()}")
    
    def update_signals_data(self):
        """Atualiza dados de sinais na interface"""
        try:
            if not self.analyzer:
                return
            
            # Obtém sinais ativos
            active_signals = self.analyzer.get_active_signals()
            
            # Atualiza contador de sinais
            if 'signals_count' in self.tags:
                dpg.set_value(self.tags['signals_count'], f"Sinais ativos: {len(active_signals)}")
            
            if 'signals_last_scan' in self.tags:
                last_scan = datetime.now().strftime("%H:%M:%S")
                dpg.set_value(self.tags['signals_last_scan'], f"Ultimo scan: {last_scan}")
            
            # Atualiza tabela de sinais
            self._update_signals_table(active_signals)
            
        except Exception as e:
            logger.error(f"Erro ao atualizar dados de sinais: {e}")
    
    def _update_signals_table(self, signals):
        """Atualiza tabela de sinais"""
        try:
            # Limpa tabela existente
            if dpg.does_item_exist("signals_table"):
                # Remove todas as linhas da tabela
                children = dpg.get_item_children("signals_table", slot=1)  # slot 1 = children
                if children:
                    for child in children:
                        dpg.delete_item(child)
                
                if not signals:
                    # Mostra mensagem quando não há sinais
                    with dpg.table_row(parent="signals_table"):
                        dpg.add_text("Nenhum sinal", color=[255, 255, 0])
                        dpg.add_text("---", color=[128, 128, 128])
                        dpg.add_text("---", color=[128, 128, 128])
                        dpg.add_text("---", color=[128, 128, 128])
                        dpg.add_text("---", color=[128, 128, 128])
                        dpg.add_text("---", color=[128, 128, 128])
                        dpg.add_text("Aguardando", color=[255, 255, 0])
                else:
                    # Adiciona sinais ativos
                    for i, signal in enumerate(signals[:10]):  # Limita a 10 sinais para performance
                        expires_in = (signal.expires_at - datetime.now()).total_seconds()
                        expires_str = f"{int(expires_in)}s" if expires_in > 0 else "Expirado"
                        
                        # Cores baseadas no tipo de sinal
                        signal_type_color = [0, 255, 0] if signal.signal_type == 'BUY' else [255, 100, 100]
                        
                        # Cor do score baseada na qualidade
                        if signal.score >= 80:
                            score_color = [0, 255, 0]  # Verde
                        elif signal.score >= 60:
                            score_color = [255, 255, 0]  # Amarelo
                        else:
                            score_color = [255, 100, 100]  # Vermelho
                        
                        with dpg.table_row(parent="signals_table", tag=f"signal_row_{i}"):
                            dpg.add_text(signal.pair_name[:12])  # Trunca nome do par
                            dpg.add_text(signal.signal_type, color=signal_type_color)
                            dpg.add_text(f"{signal.z_score:.2f}")
                            dpg.add_text(f"{signal.score:.0f}", color=score_color)
                            dpg.add_text(f"{signal.suggested_volume1:.2f}")
                            dpg.add_text(f"{signal.suggested_volume2:.2f}")
                            if expires_in > 0:
                                dpg.add_text(expires_str, color=[0, 255, 0])
                            else:
                                dpg.add_text(expires_str, color=[255, 0, 0])
                    
                    # Atualiza informações do primeiro sinal automaticamente
                    if signals:
                        self._auto_select_first_signal(signals[0])
            
        except Exception as e:
            logger.error(f"Erro ao atualizar tabela de sinais: {e}")
    
    def _auto_select_first_signal(self, signal):
        """Atualiza automaticamente os detalhes do primeiro sinal"""
        try:
            # Atualiza detalhes básicos
            details_text = (
                f"PAR: {signal.pair_name}\n"
                f"TIPO: {signal.signal_type}\n"
                f"Z-SCORE: {signal.z_score:.3f}\n"
                f"SCORE: {signal.score:.1f}/100\n"
                f"CONFIANÇA: {signal.confidence:.1%}\n"
                f"TIMEFRAME: {signal.timeframe}min\n"
                f"VOLUME 1: {signal.suggested_volume1:.4f}\n"
                f"VOLUME 2: {signal.suggested_volume2:.4f}\n"
                f"MARGEM REQ.: ${signal.margin_required:.2f}\n"
                f"TIMESTAMP: {signal.timestamp.strftime('%H:%M:%S')}\n"
                f"EXPIRA EM: {signal.expires_at.strftime('%H:%M:%S')}"
            )
            
            if 'signal_details' in self.tags:
                dpg.set_value(self.tags['signal_details'], details_text)
            
            # Atualiza critérios de forma resumida
            criteria_text = "CRITÉRIOS APROVADOS:\n"
            if hasattr(signal, 'criteria') and signal.criteria:
                passed_count = 0
                total_count = 0
                
                for criterion_name, criterion_data in signal.criteria.items():
                    if criterion_name == 'summary':
                        continue
                    
                    total_count += 1
                    passes = criterion_data.get('passes', False)
                    if passes:
                        passed_count += 1
                        
                    status_icon = "✅" if passes else "❌"
                    value = criterion_data.get('value', 0)
                    criteria_text += f"{status_icon} {criterion_name.title()}: {value:.3f}\n"
                
                criteria_text += f"\nRESULTADO: {passed_count}/{total_count} critérios aprovados"
                
                if passed_count == total_count:
                    criteria_text += " ✅"
                elif passed_count >= total_count * 0.8:
                    criteria_text += " ⚠️"
                else:
                    criteria_text += " ❌"
            else:
                criteria_text += "Critérios não disponíveis"
            
            if 'signal_criteria' in self.tags:
                dpg.set_value(self.tags['signal_criteria'], criteria_text)
            
            # Atualiza status de execução
            if 'signal_execution_status' in self.tags:
                if signal.score >= 70:
                    status = f"✅ Pronto para execução (Score: {signal.score:.1f})"
                elif signal.score >= 60:
                    status = f"⚠️ Qualidade moderada (Score: {signal.score:.1f})"
                else:
                    status = f"❌ Baixa qualidade (Score: {signal.score:.1f})"
                
                dpg.set_value(self.tags['signal_execution_status'], status)
            
        except Exception as e:
            logger.error(f"Erro ao auto-selecionar sinal: {e}")
    
    # Callbacks dos botões
    def start_trading(self):
        logger.info("🚀 Iniciando trading...")
        
    def pause_trading(self):
        """Pausa trading automático"""
        try:
            logger.info("⏸️ Pausando trading...")
            
            if self.auto_trader and self.auto_trader.running:
                self.auto_trader.stop_trading()
                logger.info("✅ Trading pausado")
                
                if 'trading_status' in self.tags:
                    dpg.set_value(self.tags['trading_status'], "Status: Pausado")
            else:
                logger.warning("Trading não está ativo")
                
        except Exception as e:
            logger.error(f"Erro ao pausar trading: {e}")
    
    def stop_trading(self):
        logger.info("🛑 Parando trading...")
        
    def reconnect_mt5(self):
        logger.info("🔄 Reconectando MT5...")
        if self.mt5:
            self.mt5.connect()
    
    def scan_pairs(self):
        logger.info("🔍 Buscando pares forex...")
        if self.mt5:
            self.mt5.get_forex_pairs(force_scan=True)
    
    def analyze_selected(self):
        """Analisa pares selecionados"""
        try:
            logger.info("📊 Analisando pares selecionados...")
            
            if not self.analyzer:
                logger.warning("Sistema de análise não disponível")
                return
            
            # Força atualização de análise
            self.analyzer.force_analysis_update()
            
            # Atualiza dados na interface
            self.update_signals_data()
            
            logger.info("✅ Análise de pares concluída")
            
        except Exception as e:
            logger.error(f"Erro ao analisar pares selecionados: {e}")
    
    def calculate_correlations(self):
        """Calcula correlações entre pares"""
        try:
            logger.info("🧮 Calculando correlações...")
            
            if not self.mt5 or not self.mt5.check_connection():
                logger.warning("MT5 não conectado")
                return
            
            # Obtém pares forex
            forex_pairs = self.mt5.get_forex_pairs()
            if not forex_pairs:
                logger.warning("Nenhum par forex disponível")
                return
            
            logger.info(f"📊 Iniciando cálculo de correlações para {len(forex_pairs)} pares")
            
            # Para demonstração, mostra quantos pares estão disponíveis
            if 'logs_text' in self.tags:
                timestamp = datetime.now().strftime("%H:%M:%S")
                log_entry = f"{timestamp} - Calculando correlações para {len(forex_pairs)} pares\n"
                current_logs = dpg.get_value(self.tags['logs_text'])
                dpg.set_value(self.tags['logs_text'], current_logs + log_entry)
            
            logger.info("✅ Cálculo de correlações iniciado")
            
        except Exception as e:
            logger.error(f"Erro ao calcular correlações: {e}")
    
    def save_config(self):
        """Salva configuração atual"""
        try:
            logger.info("💾 Salvando configuração...")
            if self.config:
                self.config.save_user_config()
                logger.info("✅ Configuração salva com sucesso")
                
                # Atualiza logs na interface
                if 'logs_text' in self.tags:
                    timestamp = datetime.now().strftime("%H:%M:%S")
                    log_entry = f"{timestamp} - Configuração salva com sucesso\n"
                    current_logs = dpg.get_value(self.tags['logs_text'])
                    dpg.set_value(self.tags['logs_text'], current_logs + log_entry)
            else:
                logger.warning("⚠️ Gerenciador de configuração não disponível")
        except Exception as e:
            logger.error(f"Erro ao salvar configuração: {e}")
    
    def load_config(self):
        """Recarrega configuração"""
        try:
            logger.info("📂 Recarregando configuração...")
            if self.config:
                self.config.reload()
                logger.info("✅ Configuração recarregada")
                
                # Atualiza logs na interface
                if 'logs_text' in self.tags:
                    timestamp = datetime.now().strftime("%H:%M:%S")
                    log_entry = f"{timestamp} - Configuração recarregada\n"
                    current_logs = dpg.get_value(self.tags['logs_text'])
                    dpg.set_value(self.tags['logs_text'], current_logs + log_entry)
            else:
                logger.warning("⚠️ Gerenciador de configuração não disponível")
        except Exception as e:
            logger.error(f"Erro ao recarregar configuração: {e}")
    
    def export_logs(self):
        """Exporta logs do sistema"""
        try:
            import shutil
            from pathlib import Path
            
            logger.info("📤 Exportando logs...")
            
            # Cria diretório de export
            export_dir = Path("exports")
            export_dir.mkdir(exist_ok=True)
            
            # Nome do arquivo com timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            export_file = export_dir / f"bancomat4_logs_{timestamp}.txt"
            
            # Copia logs
            log_file = Path("logs") / "bancomat4.log"
            if log_file.exists():
                shutil.copy2(log_file, export_file)
                logger.info(f"✅ Logs exportados para {export_file}")
                
                # Atualiza logs na interface
                if 'logs_text' in self.tags:
                    timestamp_ui = datetime.now().strftime("%H:%M:%S")
                    log_entry = f"{timestamp_ui} - Logs exportados para {export_file.name}\n"
                    current_logs = dpg.get_value(self.tags['logs_text'])
                    dpg.set_value(self.tags['logs_text'], current_logs + log_entry)
            else:
                logger.warning("⚠️ Arquivo de log não encontrado")
                
        except Exception as e:
            logger.error(f"Erro ao exportar logs: {e}")
    
    def show_manual(self):
        """Mostra manual do usuário"""
        try:
            import webbrowser
            import os
            
            logger.info("📖 Abrindo manual...")
            
            # Verifica se README existe
            readme_path = Path("README.md")
            if readme_path.exists():
                # Tenta abrir com aplicativo padrão
                if os.name == 'nt':  # Windows
                    os.startfile(str(readme_path))
                else:  # Linux/Mac
                    webbrowser.open(f"file://{readme_path.absolute()}")
                
                logger.info("✅ Manual aberto")
            else:
                logger.warning("⚠️ Arquivo README.md não encontrado")
                
                # Mostra informações básicas na interface
                if 'logs_text' in self.tags:
                    timestamp = datetime.now().strftime("%H:%M:%S")
                    manual_info = (
                        f"{timestamp} - MANUAL BÁSICO:\n"
                        "1. Conecte ao MT5\n"
                        "2. Vá para aba Trading e clique 'Iniciar Trading'\n"
                        "3. Monitore sinais na aba Sinais\n"
                        "4. Acompanhe trades na aba Dashboard\n\n"
                    )
                    current_logs = dpg.get_value(self.tags['logs_text'])
                    dpg.set_value(self.tags['logs_text'], current_logs + manual_info)
                    
        except Exception as e:
            logger.error(f"Erro ao abrir manual: {e}")
    
    def show_about(self):
        """Mostra informações sobre o sistema"""
        try:
            logger.info("🏦 Mostrando informações do sistema...")
            
            # Informações do sistema
            about_info = (
                "🏦 BANCOMAT 4 - Expert Advisor\n"
                "Versão: 4.0.0\n"
                "Sistema de trading automatizado para pares forex\n"
                "Baseado em análise estatística e mean reversion\n\n"
                "Recursos:\n"
                "• Análise de cointegração e correlação\n"
                "• Geração automática de sinais\n"
                "• Trading automático\n"
                "• Gestão avançada de risco\n"
                "• Interface gráfica moderna\n\n"
            )
            
            # Exibe informações nos logs
            if 'logs_text' in self.tags:
                timestamp = datetime.now().strftime("%H:%M:%S")
                log_entry = f"{timestamp} - {about_info}"
                current_logs = dpg.get_value(self.tags['logs_text'])
                dpg.set_value(self.tags['logs_text'], current_logs + log_entry)
            
            logger.info("✅ Informações do sistema exibidas")
            
        except Exception as e:
            logger.error(f"Erro ao mostrar informações: {e}")
    
    def quit_app(self):
        logger.info("👋 Encerrando aplicação...")
        self.running = False
        dpg.stop_dearpygui()
    
    # Métodos adicionais (implementar conforme necessário)
    def filter_pairs(self): pass
    def refresh_pairs(self): 
        """Callback para atualizar lista de pares"""
        logger.info("Atualizando lista de pares...")
        self.update_pairs_list()
    def analyze_selected_pair(self):
        """Analisa o par selecionado"""
        try:
            if not self.analyzer:
                logger.warning("Sistema de análise não disponível")
                return
            
            # Obtém par selecionado
            selected_pair = dpg.get_value("pairs_listbox")
            if not selected_pair or selected_pair == "Carregando...":
                logger.warning("Nenhum par selecionado para análise")
                return
            
            logger.info(f"🔬 Iniciando análise detalhada de {selected_pair}")
            
            # Realiza análise completa
            analysis = self.analyzer.analyze_pair(selected_pair)
            
            if analysis:
                # Exibe resultados da análise
                self._display_analysis_results(analysis)
                logger.info(f"✅ Análise de {selected_pair} concluída - Score: {analysis.score:.1f}")
            else:
                logger.warning(f"❌ Não foi possível analisar {selected_pair}")
                
        except Exception as e:
            logger.error(f"Erro ao analisar par selecionado: {e}")
    
    def _display_analysis_results(self, analysis):
        """Exibe resultados da análise na interface"""
        try:
            # Atualiza informações do par
            if 'selected_pair_info' in self.tags:
                info_text = (
                    f"Par: {analysis.pair_name}\n"
                    f"Score: {analysis.score:.1f}/100\n"
                    f"Status: {analysis.status}\n"
                    f"Z-Score: {analysis.z_score:.3f}\n"
                    f"Correlacao: {analysis.correlation:.3f}\n"
                    f"Cointegracao: {'Sim' if analysis.is_cointegrated else 'Nao'}\n"
                    f"ADF p-value: {analysis.adf_pvalue:.6f}\n"
                    f"Half-life: {analysis.half_life:.1f} periodos\n"
                    f"Beta: {analysis.beta:.4f}\n"
                    f"Alpha: {analysis.alpha:.4f}"
                )
                dpg.set_value(self.tags['selected_pair_info'], info_text)
            
            # Atualiza critérios detalhados
            criteria = analysis.criteria_details
            criteria_text = "CRITÉRIOS DE TRADING:\n"
            
            for criterion_name, criterion_data in criteria.items():
                if criterion_name == 'summary':
                    continue
                    
                passes = criterion_data.get('passes', False)
                value = criterion_data.get('value', 0)
                status_icon = "✅" if passes else "❌"
                
                if criterion_name == 'zscore':
                    criteria_text += f"{status_icon} Z-Score: {value:.3f} (min: {criterion_data.get('min_threshold', 0)}, max: {criterion_data.get('max_threshold', 0)})\n"
                elif criterion_name == 'correlation':
                    criteria_text += f"{status_icon} Correlacao: {value:.3f} (min: {criterion_data.get('min_threshold', 0):.2f})\n"
                elif criterion_name == 'cointegration':
                    criteria_text += f"{status_icon} Cointegracao p-value: {value:.6f} (max: {criterion_data.get('threshold', 0):.3f})\n"
                elif criterion_name == 'half_life':
                    criteria_text += f"{status_icon} Half-life: {value:.1f} (range: {criterion_data.get('min_threshold', 0)}-{criterion_data.get('max_threshold', 0)})\n"
            
            # Resultado final
            summary = criteria.get('summary', {})
            passed_count = summary.get('passed_count', 0)
            total_count = summary.get('total_count', 0)
            all_pass = summary.get('all_pass', False)
            
            criteria_text += f"\nRESULTADO: {passed_count}/{total_count} criterios aprovados\n"
            criteria_text += f"STATUS: {'APROVADO' if all_pass else 'REJEITADO'} para trading"
            
            # Adiciona aos logs da interface
            if 'logs_text' in self.tags:
                timestamp = datetime.now().strftime("%H:%M:%S")
                log_entry = f"{timestamp} - Analise {analysis.pair_name}: Score {analysis.score:.1f}, Status {analysis.status}\n"
                current_logs = dpg.get_value(self.tags['logs_text'])
                dpg.set_value(self.tags['logs_text'], current_logs + log_entry)
            
            logger.info(f"📊 Resultados da análise exibidos para {analysis.pair_name}")
            
        except Exception as e:
            logger.error(f"Erro ao exibir resultados da análise: {e}")
    def favorite_pair(self): pass
    def start_auto_trading(self):
        """Inicia trading automático com verificações robustas"""
        try:
            logger.info("🚀 Tentando iniciar trading automático...")
            
            # Verifica se AutoTrader está disponível
            if not hasattr(self, 'auto_trader') or self.auto_trader is None:
                logger.warning("⚠️ AutoTrader não disponível, tentando inicializar...")
                self.initialize_auto_trader()
                
                if self.auto_trader is None:
                    logger.error("❌ Não foi possível inicializar AutoTrader")
                    if 'trading_status' in self.tags:
                        dpg.set_value(self.tags['trading_status'], 
                                     "Status: Erro - AutoTrader não disponível")
                    return
            
            # Verifica conexão MT5
            if not self.mt5 or not self.mt5.is_connected():
                logger.error("❌ MT5 não conectado")
                if 'trading_status' in self.tags:
                    dpg.set_value(self.tags['trading_status'], 
                                 "Status: Erro - MT5 desconectado")
                return
            
            # Verifica se já está rodando
            if hasattr(self.auto_trader, 'running') and self.auto_trader.running:
                logger.warning("⚠️ AutoTrader já está rodando")
                if 'trading_status' in self.tags:
                    dpg.set_value(self.tags['trading_status'], 
                                 "Status: Trading Automático")
                return
            
            # Inicia o trading
            if hasattr(self.auto_trader, 'start_trading'):
                self.auto_trader.start_trading()
                logger.info("✅ Trading automático iniciado")
                
                # Atualiza interface
                if 'trading_status' in self.tags:
                    dpg.set_value(self.tags['trading_status'], 
                                 "Status: Trading Automático")
                if 'trading_mode' in self.tags:
                    dpg.set_value(self.tags['trading_mode'], 
                                 "Modo: Automático")
            else:
                logger.error("❌ Método start_trading não encontrado no AutoTrader")
                
        except Exception as e:
            logger.error(f"❌ Erro ao iniciar trading automático: {e}")
            if 'trading_status' in self.tags:
                dpg.set_value(self.tags['trading_status'], 
                             f"Status: Erro - {str(e)[:30]}")
    def stop_and_close_all(self): 
        """Para trading e fecha todas as posições"""
        try:
            logger.info("🛑 Parando trading e fechando posições...")
            
            if 'trading_status' in self.tags:
                dpg.set_value(self.tags['trading_status'], "Status: Parando...")
            
            # Para o trading automático
            if self.auto_trader and self.auto_trader.running:
                # Fecha todos os trades primeiro
                self.auto_trader.close_all_trades("Parada manual")
                
                # Para o sistema
                self.auto_trader.stop_trading()
                logger.info("✅ Trading automático parado")
            
            # Para o analyzer
            if self.analyzer and self.analyzer.running:
                self.analyzer.stop_analysis()
                logger.info("✅ Sistema de análise parado")
            
            if 'trading_status' in self.tags:
                dpg.set_value(self.tags['trading_status'], "Status: Parado")
                
        except Exception as e:
            logger.error(f"Erro ao parar trading: {e}")
            
            if 'trading_status' in self.tags:
                dpg.set_value(self.tags['trading_status'], f"Status: Erro - {str(e)}")
    def open_manual_trade(self): pass
    def close_selected_trades(self): pass
    def refresh_trades(self): 
        logger.info("Atualizando lista de trades...")
        if 'trades_info' in self.tags:
            dpg.set_value(self.tags['trades_info'], "Lista atualizada...")
    def show_trade_stats(self): 
        logger.info("Mostrando estatisticas de trades...")
    def export_trades(self): pass
    def close_selected_trade(self): pass
    def generate_report(self): 
        """Gera relatório de estatísticas"""
        try:
            logger.info("📊 Gerando relatório...")
            
            # Coleta dados do sistema
            stats = {
                'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                'mt5_connected': self.mt5.check_connection() if self.mt5 else False,
                'analyzer_running': self.analyzer.running if self.analyzer else False,
                'trader_running': self.auto_trader.running if self.auto_trader else False
            }
            
            # Dados do auto trader se disponível
            if self.auto_trader:
                trader_stats = self.auto_trader.get_stats()
                stats.update(trader_stats)
                
                active_trades = self.auto_trader.get_active_trades()
                stats['active_trades_count'] = len(active_trades)
                
                if active_trades:
                    total_profit = sum(trade.total_profit for trade in active_trades)
                    stats['current_profit'] = total_profit
            
            # Dados do analyzer se disponível
            if self.analyzer:
                signals = self.analyzer.get_active_signals()
                stats['active_signals'] = len(signals)
                
                if signals:
                    avg_score = sum(signal.score for signal in signals) / len(signals)
                    stats['average_signal_score'] = avg_score
            
            # Dados da conta
            if self.mt5 and self.mt5.check_connection():
                account_info = self.mt5.get_account_info()
                if account_info:
                    stats['account_balance'] = account_info.balance
                    stats['account_equity'] = account_info.equity
                    stats['account_margin_level'] = account_info.margin_level
            
            # Cria relatório
            report = "📊 RELATÓRIO BANCOMAT 4\n"
            report += "=" * 40 + "\n\n"
            
            for key, value in stats.items():
                report += f"{key.replace('_', ' ').title()}: {value}\n"
            
            # Exibe nos logs
            if 'logs_text' in self.tags:
                current_logs = dpg.get_value(self.tags['logs_text'])
                dpg.set_value(self.tags['logs_text'], current_logs + "\n" + report + "\n")
            
            # Salva em arquivo
            reports_dir = Path("reports")
            reports_dir.mkdir(exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_file = reports_dir / f"bancomat4_report_{timestamp}.txt"
            
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report)
            
            logger.info(f"✅ Relatório gerado: {report_file}")
            
        except Exception as e:
            logger.error(f"Erro ao gerar relatório: {e}")
    
    def export_stats(self): pass
    def reset_stats(self): pass
    def scan_signals(self): 
        """Busca novos sinais de trading"""
        try:
            logger.info("🔍 Buscando sinais de trading...")
            
            if not self.analyzer:
                logger.warning("Sistema de análise não disponível")
                return
            
            # Inicia ou força atualização da análise
            if not self.analyzer.running:
                self.analyzer.start_analysis()
                logger.info("🚀 Sistema de análise iniciado")
            else:
                self.analyzer.force_analysis_update()
                logger.info("🔄 Análise forçada")
            
            # Atualiza interface
            self.update_signals_data()
            
        except Exception as e:
            logger.error(f"Erro ao buscar sinais: {e}")
    
    def refresh_signals(self): 
        """Atualiza lista de sinais"""
        try:
            logger.info("🔄 Atualizando sinais...")
            self.update_signals_data()
        except Exception as e:
            logger.error(f"Erro ao atualizar sinais: {e}")
    
    def clear_expired_signals(self): 
        """Remove sinais expirados"""
        try:
            if not self.analyzer:
                return
            
            before_count = len(self.analyzer.get_active_signals())
            self.analyzer._cleanup_expired_signals()
            after_count = len(self.analyzer.get_active_signals())
            
            removed_count = before_count - after_count
            
            if removed_count > 0:
                logger.info(f"🧹 {removed_count} sinais expirados removidos")
            else:
                logger.info("✅ Nenhum sinal expirado encontrado")
            
            self.update_signals_data()
            
        except Exception as e:
            logger.error(f"Erro ao limpar sinais expirados: {e}")
    
    def execute_signal(self): 
        """Executa sinal selecionado"""
        try:
            logger.info("📈 Executando sinal...")
            
            # Obtém sinais ativos do analyzer
            if not self.analyzer:
                self._log_to_interface("❌ Sistema de análise não disponível")
                return
            
            active_signals = self.analyzer.get_active_signals()
            if not active_signals:
                self._log_to_interface("❌ Nenhum sinal ativo disponível para execução")
                return
            
            # Usa o primeiro sinal disponível (ou implementar seleção)
            signal = active_signals[0]
            
            # Verifica se auto trader está disponível
            if not self.auto_trader:
                self._log_to_interface("❌ Sistema de trading automático não disponível")
                return
            
            # Verifica se deve confirmar antes de executar
            should_confirm = False
            if dpg.does_item_exist("signal_confirm"):
                should_confirm = dpg.get_value("signal_confirm")
            
            if should_confirm:
                self._log_to_interface(f"⚠️ Confirmação necessária para executar {signal.pair_name}")
                self._log_to_interface("💡 Desmarque 'Confirmar antes de executar' para execução direta")
                
                # Atualiza status
                if 'signal_execution_status' in self.tags:
                    dpg.set_value(self.tags['signal_execution_status'], "Aguardando confirmação do usuário")
                return
            
            # Obtém volumes customizados se especificados
            custom_volume = None
            if dpg.does_item_exist("signal_volume"):
                custom_volume = dpg.get_value("signal_volume")
                if custom_volume > 0 and custom_volume != signal.suggested_volume1:
                    # Aplica volume customizado
                    original_vol1 = signal.suggested_volume1
                    original_vol2 = signal.suggested_volume2
                    
                    signal.suggested_volume1 = custom_volume
                    signal.suggested_volume2 = custom_volume  # Simplificado
                    
                    self._log_to_interface(f"📊 Volume customizado aplicado: {custom_volume:.4f}")
                    logger.info(f"Volume customizado: {original_vol1:.4f}->{custom_volume:.4f}")
            
            # Atualiza status para executando
            if 'signal_execution_status' in self.tags:
                dpg.set_value(self.tags['signal_execution_status'], "🚀 Executando trade...")
            
            # Executa o sinal
            if self.auto_trader._should_execute_signal(signal):
                success = self.auto_trader._execute_signal(signal)
                if success:
                    self._log_to_interface(f"✅ Sinal {signal.pair_name} executado com sucesso")
                    if 'signal_execution_status' in self.tags:
                        dpg.set_value(self.tags['signal_execution_status'], "✅ Trade executado com sucesso")
                    logger.info(f"✅ Sinal {signal.pair_name} executado manualmente")
                else:
                    self._log_to_interface(f"❌ Falha ao executar sinal {signal.pair_name}")
                    if 'signal_execution_status' in self.tags:
                        dpg.set_value(self.tags['signal_execution_status'], "❌ Falha na execução")
                    logger.error(f"❌ Falha ao executar sinal {signal.pair_name}")
            else:
                self._log_to_interface(f"⚠️ Sinal {signal.pair_name} não atende critérios de execução")
                if 'signal_execution_status' in self.tags:
                    dpg.set_value(self.tags['signal_execution_status'], "⚠️ Critérios não atendidos")
                logger.warning(f"⚠️ Sinal {signal.pair_name} não atende critérios de execução")
                
        except Exception as e:
            logger.error(f"Erro ao executar sinal: {e}")
            self._log_to_interface(f"❌ Erro ao executar sinal: {str(e)}")
            if 'signal_execution_status' in self.tags:
                dpg.set_value(self.tags['signal_execution_status'], f"❌ Erro: {str(e)[:50]}")
    
    def schedule_signal(self): 
        """Agenda sinal para execução automática"""
        try:
            logger.info("⏰ Agendando sinal...")
            
            # Obtém sinais ativos
            if not self.analyzer:
                self._log_to_interface("❌ Sistema de análise não disponível")
                return
            
            active_signals = self.analyzer.get_active_signals()
            if not active_signals:
                self._log_to_interface("❌ Nenhum sinal ativo para agendar")
                return
            
            # Inicia o auto trader se não estiver rodando
            if not self.auto_trader:
                self._log_to_interface("❌ Sistema de trading automático não disponível")
                return
            
            if not self.auto_trader.running:
                self.auto_trader.start_trading()
                self._log_to_interface("🚀 Trading automático iniciado - sinais serão executados automaticamente")
                logger.info("🚀 Trading automático iniciado via agendamento de sinal")
            else:
                self._log_to_interface("✅ Trading automático já ativo - sinais sendo processados automaticamente")
                logger.info("✅ Trading automático já ativo")
                
        except Exception as e:
            logger.error(f"Erro ao agendar sinal: {e}")
            self._log_to_interface(f"❌ Erro ao agendar sinal: {str(e)}")
    
    def ignore_signal(self): 
        """Remove/ignora sinal selecionado"""
        try:
            logger.info("❌ Ignorando sinal...")
            
            if not self.analyzer:
                self._log_to_interface("❌ Sistema de análise não disponível")
                return
            
            active_signals = self.analyzer.get_active_signals()
            if not active_signals:
                self._log_to_interface("❌ Nenhum sinal ativo para ignorar")
                return
            
            # Remove o primeiro sinal (ou implementar seleção específica)
            signal_to_remove = active_signals[0]
            
            # Remove da cache de sinais
            self.analyzer.signals_cache = [s for s in self.analyzer.signals_cache 
                                         if s.pair_name != signal_to_remove.pair_name]
            
            self._log_to_interface(f"🗑️ Sinal {signal_to_remove.pair_name} removido/ignorado")
            logger.info(f"🗑️ Sinal {signal_to_remove.pair_name} removido manualmente")
            
            # Atualiza interface
            self.update_signals_data()
            
        except Exception as e:
            logger.error(f"Erro ao ignorar sinal: {e}")
            self._log_to_interface(f"❌ Erro ao ignorar sinal: {str(e)}")
    
    def _log_to_interface(self, message: str):
        """Adiciona mensagem aos logs da interface"""
        try:
            if 'logs_text' in self.tags:
                timestamp = datetime.now().strftime("%H:%M:%S")
                log_entry = f"{timestamp} - {message}\n"
                current_logs = dpg.get_value(self.tags['logs_text'])
                dpg.set_value(self.tags['logs_text'], current_logs + log_entry)
        except Exception as e:
            logger.error(f"Erro ao adicionar log à interface: {e}")
    
    def analyze_selected_signal(self):
        """Analisa detalhadamente o sinal selecionado"""
        try:
            logger.info("📊 Analisando sinal detalhadamente...")
            
            if not self.analyzer:
                self._log_to_interface("❌ Sistema de análise não disponível")
                return
            
            active_signals = self.analyzer.get_active_signals()
            if not active_signals:
                self._log_to_interface("❌ Nenhum sinal ativo para analisar")
                return
            
            # Analisa o primeiro sinal (melhorar para seleção específica)
            signal = active_signals[0]
            
            # Obtém análise completa do par
            analysis = self.analyzer.get_pair_analysis(signal.pair_name)
            
            if analysis:
                # Atualiza detalhes do sinal
                details_text = (
                    f"PAR: {signal.pair_name}\n"
                    f"TIPO: {signal.signal_type}\n"
                    f"Z-SCORE: {signal.z_score:.3f}\n"
                    f"CORRELAÇÃO: {signal.correlation:.3f}\n"
                    f"SCORE: {signal.score:.1f}/100\n"
                    f"BETA: {signal.beta:.4f}\n"
                    f"HALF-LIFE: {signal.half_life:.1f} períodos\n"
                    f"CONFIANÇA: {signal.confidence:.1%}\n"
                    f"TIMEFRAME: {signal.timeframe}min\n"
                    f"VOLUME 1: {signal.suggested_volume1:.4f}\n"
                    f"VOLUME 2: {signal.suggested_volume2:.4f}\n"
                    f"MARGEM REQ.: ${signal.margin_required:.2f}"
                )
                
                if 'signal_details' in self.tags:
                    dpg.set_value(self.tags['signal_details'], details_text)
                
                # Atualiza critérios
                criteria_text = "CRITÉRIOS ESTATÍSTICOS:\n"
                for criterion_name, criterion_data in signal.criteria.items():
                    if criterion_name == 'summary':
                        continue
                    passes = criterion_data.get('passes', False)
                    status_icon = "✅" if passes else "❌"
                    value = criterion_data.get('value', 0)
                    criteria_text += f"{status_icon} {criterion_name.title()}: {value:.3f}\n"
                
                if 'signal_criteria' in self.tags:
                    dpg.set_value(self.tags['signal_criteria'], criteria_text)
                
                self._log_to_interface(f"📊 Análise detalhada do sinal {signal.pair_name} atualizada")
            else:
                self._log_to_interface(f"❌ Não foi possível obter análise para {signal.pair_name}")
                
        except Exception as e:
            logger.error(f"Erro ao analisar sinal: {e}")
            self._log_to_interface(f"❌ Erro ao analisar sinal: {str(e)}")
    
    def copy_signal_info(self):
        """Copia informações do sinal para clipboard"""
        try:
            logger.info("📋 Copiando informações do sinal...")
            
            if not self.analyzer:
                self._log_to_interface("❌ Sistema de análise não disponível")
                return
            
            active_signals = self.analyzer.get_active_signals()
            if not active_signals:
                self._log_to_interface("❌ Nenhum sinal ativo para copiar")
                return
            
            signal = active_signals[0]
            
            # Cria texto formatado
            signal_text = (
                f"🔔 SINAL BANCOMAT 4\n"
                f"═══════════════════\n"
                f"Par: {signal.pair_name}\n"
                f"Tipo: {signal.signal_type}\n"
                f"Z-Score: {signal.z_score:.3f}\n"
                f"Score: {signal.score:.1f}/100\n"
                f"Correlação: {signal.correlation:.3f}\n"
                f"Confiança: {signal.confidence:.1%}\n"
                f"Volume1: {signal.suggested_volume1:.4f}\n"
                f"Volume2: {signal.suggested_volume2:.4f}\n"
                f"Timestamp: {signal.timestamp.strftime('%Y-%m-%d %H:%M:%S')}\n"
                f"Expira em: {signal.expires_at.strftime('%H:%M:%S')}"
            )
            
            # Tenta copiar para clipboard (simples fallback)
            try:
                import pyperclip
                pyperclip.copy(signal_text)
                self._log_to_interface("📋 Informações do sinal copiadas para clipboard")
            except ImportError:
                # Fallback: salva em arquivo temporário
                with open('temp_signal_info.txt', 'w', encoding='utf-8') as f:
                    f.write(signal_text)
                self._log_to_interface("📋 Informações salvas em temp_signal_info.txt")
            
        except Exception as e:
            logger.error(f"Erro ao copiar informações: {e}")
            self._log_to_interface(f"❌ Erro ao copiar informações: {str(e)}")
    
    def configure_signal_alerts(self):
        """Configura alertas para sinais"""
        try:
            logger.info("⚠️ Configurando alertas...")
            
            # Por enquanto, apenas ativa/desativa alertas visuais
            self._log_to_interface("⚠️ Sistema de alertas configurado")
            self._log_to_interface("🔔 Alertas visuais ativos na interface")
            self._log_to_interface("📧 Alertas por email: não implementado")
            self._log_to_interface("📱 Alertas push: não implementado")
            
            # Atualiza status
            if 'signal_execution_status' in self.tags:
                dpg.set_value(self.tags['signal_execution_status'], "Alertas configurados")
            
        except Exception as e:
            logger.error(f"Erro ao configurar alertas: {e}")
            self._log_to_interface(f"❌ Erro ao configurar alertas: {str(e)}")
    
    def refresh_logs(self): 
        logger.info("Atualizando logs...")
        if 'logs_text' in self.tags:
            current_logs = dpg.get_value(self.tags['logs_text'])
            new_log = f"{datetime.now().strftime('%H:%M:%S')} - Logs atualizados\n"
            dpg.set_value(self.tags['logs_text'], current_logs + new_log)
    
    def clear_logs(self): pass
    def pause_logs(self): pass
    def restore_defaults(self): pass
    def open_config_file(self): pass
    def open_manual(self): pass
    def report_bug(self): pass
    def open_support(self): pass
    
    def update_pairs_analysis_data(self):
        """Atualiza dados de análise dos pares"""
        try:
            if not self.analyzer:
                return
            
            # Obtém todas as análises disponíveis
            all_analyses = self.analyzer.get_all_analyses()
            
            # Converte para formato da tabela
            self.pairs_analysis_data = []
            
            for pair_name, analysis in all_analyses.items():
                if analysis:
                    # Determina cores baseadas nos critérios
                    score_color = self._get_score_color(analysis.score)
                    status_color = self._get_status_color(analysis.status)
                    
                    pair_data = {
                        'pair_name': pair_name,
                        'score': analysis.score,
                        'status': analysis.status,
                        'z_score': analysis.z_score,
                        'correlation': analysis.correlation,
                        'cointegration_pvalue': analysis.adf_pvalue,
                        'half_life': analysis.half_life,
                        'beta': analysis.beta,
                        'confidence': analysis.confidence if hasattr(analysis, 'confidence') else 0.0,
                        'last_analysis': analysis.timestamp.strftime("%H:%M:%S") if hasattr(analysis, 'timestamp') else "---",
                        'analysis_object': analysis,  # Referência para detalhes
                        'score_color': score_color,
                        'status_color': status_color
                    }
                    
                    self.pairs_analysis_data.append(pair_data)
            
            # Atualiza estatísticas
            self._update_pairs_statistics()
            
            # Atualiza tabela
            self.update_pairs_table()
            
        except Exception as e:
            logger.error(f"Erro ao atualizar dados de análise: {e}")
    
    def _get_score_color(self, score):
        """Retorna cor baseada no score"""
        if score >= 80:
            return [0, 255, 0]      # Verde forte
        elif score >= 70:
            return [144, 238, 144]  # Verde claro
        elif score >= 60:
            return [255, 255, 0]    # Amarelo
        elif score >= 40:
            return [255, 165, 0]    # Laranja
        else:
            return [255, 100, 100]  # Vermelho
    
    def _get_status_color(self, status):
        """Retorna cor baseada no status"""
        if status in ['BUY_SIGNAL', 'SELL_SIGNAL']:
            return [0, 255, 0]      # Verde - sinal ativo
        elif status == 'APPROVED':
            return [144, 238, 144]  # Verde claro - aprovado
        elif status == 'SCORE_BAIXO':
            return [255, 255, 0]    # Amarelo - score baixo
        else:
            return [255, 100, 100]  # Vermelho - rejeitado
    
    def _get_criteria_color(self, value, criterion_type, threshold_min=None, threshold_max=None):
        """Retorna cor baseada se critério é atendido"""
        try:
            if criterion_type == 'z_score':
                # Z-Score: aprovado se absoluto >= 2.0
                if abs(value) >= 2.0:
                    return [0, 255, 0]  # Verde
                elif abs(value) >= 1.5:
                    return [255, 255, 0]  # Amarelo
                else:
                    return [255, 100, 100]  # Vermelho
                    
            elif criterion_type == 'correlation':
                # Correlação: aprovado se >= 0.60
                if abs(value) >= 0.60:
                    return [0, 255, 0]  # Verde
                elif abs(value) >= 0.40:
                    return [255, 255, 0]  # Amarelo
                else:
                    return [255, 100, 100]  # Vermelho
                    
            elif criterion_type == 'cointegration':
                # Cointegração: aprovado se p-value <= 0.10
                if value <= 0.10:
                    return [0, 255, 0]  # Verde
                elif value <= 0.20:
                    return [255, 255, 0]  # Amarelo
                else:
                    return [255, 100, 100]  # Vermelho
                    
            elif criterion_type == 'half_life':
                # Half-life: aprovado se entre 5 e 96 períodos
                if 5 <= value <= 96:
                    return [0, 255, 0]  # Verde
                elif 2 <= value <= 120:
                    return [255, 255, 0]  # Amarelo
                else:
                    return [255, 100, 100]  # Vermelho
                    
            elif criterion_type == 'beta':
                # Beta: aprovado se entre 0.1 e 10.0
                if 0.1 <= abs(value) <= 10.0:
                    return [0, 255, 0]  # Verde
                elif 0.05 <= abs(value) <= 20.0:
                    return [255, 255, 0]  # Amarelo
                else:
                    return [255, 100, 100]  # Vermelho
                    
            else:
                # Cor padrão
                return [220, 220, 220]  # Branco
                
        except Exception:
            return [220, 220, 220]  # Branco em caso de erro
    
    def _update_pairs_statistics(self):
        """Atualiza estatísticas dos pares"""
        try:
            total_pairs = len(self.pairs_analysis_data)
            analyzed_pairs = sum(1 for pair in self.pairs_analysis_data if pair['score'] > 0)
            approved_pairs = sum(1 for pair in self.pairs_analysis_data if pair['status'] in ['APPROVED', 'BUY_SIGNAL', 'SELL_SIGNAL'])
            signal_pairs = sum(1 for pair in self.pairs_analysis_data if pair['status'] in ['BUY_SIGNAL', 'SELL_SIGNAL'])
            best_score = max([pair['score'] for pair in self.pairs_analysis_data], default=0)
            
            # Atualiza interface
            if 'pairs_total' in self.tags:
                dpg.set_value(self.tags['pairs_total'], f"Total de pares: {total_pairs}")
            
            if 'pairs_analyzed' in self.tags:
                dpg.set_value(self.tags['pairs_analyzed'], f"Analisados: {analyzed_pairs}")
                
            if 'pairs_approved' in self.tags:
                dpg.set_value(self.tags['pairs_approved'], f"Aprovados: {approved_pairs}")
                
            if 'pairs_with_signals' in self.tags:
                dpg.set_value(self.tags['pairs_with_signals'], f"Com sinais: {signal_pairs}")
                
            if 'pairs_best_score' in self.tags:
                dpg.set_value(self.tags['pairs_best_score'], f"Melhor score: {best_score:.1f}")
                
            if 'analysis_status' in self.tags:
                if analyzed_pairs > 0:
                    dpg.set_value(self.tags['analysis_status'], f"✅ {analyzed_pairs} pares analisados")
                else:
                    dpg.set_value(self.tags['analysis_status'], "⚠️ Aguardando análise...")
                    
            if 'analysis_last_update' in self.tags:
                dpg.set_value(self.tags['analysis_last_update'], f"Última atualização: {datetime.now().strftime('%H:%M:%S')}")
            
        except Exception as e:
            logger.error(f"Erro ao atualizar estatísticas dos pares: {e}")
    
    def update_pairs_table(self):
        """Atualiza tabela de pares com filtros e ordenação"""
        try:
            if not dpg.does_item_exist("pairs_analysis_table"):
                return
            
            # Obtém filtros
            show_approved_only = dpg.get_value("filter_approved_only") if dpg.does_item_exist("filter_approved_only") else False
            show_signals_only = dpg.get_value("filter_signals_only") if dpg.does_item_exist("filter_signals_only") else False
            min_score = dpg.get_value("filter_min_score") if dpg.does_item_exist("filter_min_score") else 0.0
            sort_method = dpg.get_value("pairs_sort_combo") if dpg.does_item_exist("pairs_sort_combo") else "Score (Maior)"
            
            # Filtra dados
            filtered_data = []
            for pair_data in self.pairs_analysis_data:
                # Aplica filtros
                if show_approved_only and pair_data['status'] not in ['APPROVED', 'BUY_SIGNAL', 'SELL_SIGNAL']:
                    continue
                
                if show_signals_only and pair_data['status'] not in ['BUY_SIGNAL', 'SELL_SIGNAL']:
                    continue
                
                if pair_data['score'] < min_score:
                    continue
                
                filtered_data.append(pair_data)
            
            # Ordena dados
            if sort_method == "Score (Maior)":
                filtered_data.sort(key=lambda x: x['score'], reverse=True)
            elif sort_method == "Score (Menor)":
                filtered_data.sort(key=lambda x: x['score'])
            elif sort_method == "Nome A-Z":
                filtered_data.sort(key=lambda x: x['pair_name'])
            elif sort_method == "Status":
                status_order = {'BUY_SIGNAL': 0, 'SELL_SIGNAL': 1, 'APPROVED': 2, 'SCORE_BAIXO': 3, 'REJEITADO': 4}
                filtered_data.sort(key=lambda x: status_order.get(x['status'], 5))
            
            # Limpa tabela existente
            children = dpg.get_item_children("pairs_analysis_table", slot=1)  # slot 1 = children
            if children:
                for child in children:
                    dpg.delete_item(child)
            
            # Adiciona dados filtrados
            for i, pair_data in enumerate(filtered_data[:50]):  # Limita a 50 para performance
                
                # Cores para cada critério
                z_score_color = self._get_criteria_color(pair_data['z_score'], 'z_score')
                correlation_color = self._get_criteria_color(pair_data['correlation'], 'correlation')
                cointegration_color = self._get_criteria_color(pair_data['cointegration_pvalue'], 'cointegration')
                half_life_color = self._get_criteria_color(pair_data['half_life'], 'half_life')
                beta_color = self._get_criteria_color(pair_data['beta'], 'beta')
                
                with dpg.table_row(parent="pairs_analysis_table", tag=f"pair_row_{i}"):
                    # Par
                    dpg.add_text(pair_data['pair_name'])
                    
                    # Score (com cor baseada no valor)
                    dpg.add_text(f"{pair_data['score']:.1f}", color=pair_data['score_color'])
                    
                    # Status (com cor baseada no status)
                    dpg.add_text(pair_data['status'], color=pair_data['status_color'])
                    
                    # Z-Score (com cor baseada no critério)
                    dpg.add_text(f"{pair_data['z_score']:.3f}", color=z_score_color)
                    
                    # Correlação (com cor baseada no critério)
                    dpg.add_text(f"{pair_data['correlation']:.3f}", color=correlation_color)
                    
                    # Cointegração (com cor baseada no critério)
                    dpg.add_text(f"{pair_data['cointegration_pvalue']:.4f}", color=cointegration_color)
                    
                    # Half-Life (com cor baseada no critério)
                    dpg.add_text(f"{pair_data['half_life']:.1f}", color=half_life_color)
                    
                    # Beta (com cor baseada no critério)
                    dpg.add_text(f"{pair_data['beta']:.3f}", color=beta_color)
                    
                    # Confiança
                    confidence_pct = pair_data['confidence'] * 100 if pair_data['confidence'] <= 1.0 else pair_data['confidence']
                    dpg.add_text(f"{confidence_pct:.1f}%")
                    
                    # Última análise
                    dpg.add_text(pair_data['last_analysis'])
            
            # Adiciona linha de "sem dados" se necessário
            if not filtered_data:
                with dpg.table_row(parent="pairs_analysis_table"):
                    dpg.add_text("Nenhum par", color=[255, 255, 0])
                    dpg.add_text("---", color=[128, 128, 128])
                    dpg.add_text("---", color=[128, 128, 128])
                    dpg.add_text("---", color=[128, 128, 128])
                    dpg.add_text("---", color=[128, 128, 128])
                    dpg.add_text("---", color=[128, 128, 128])
                    dpg.add_text("---", color=[128, 128, 128])
                    dpg.add_text("---", color=[128, 128, 128])
                    dpg.add_text("---", color=[128, 128, 128])
                    dpg.add_text("Aguardando", color=[255, 255, 0])
            
        except Exception as e:
            logger.error(f"Erro ao atualizar tabela de pares: {e}")
    
    def refresh_pairs_analysis(self):
        """Atualiza análise dos pares"""
        try:
            logger.info("🔄 Atualizando análise de pares...")
            
            if not self.analyzer:
                logger.warning("Sistema de análise não disponível")
                return
            
            # Força atualização
            self.analyzer.force_analysis_update()
            
            # Atualiza dados
            self.update_pairs_analysis_data()
            
            logger.info("✅ Análise de pares atualizada")
            
        except Exception as e:
            logger.error(f"Erro ao atualizar análise de pares: {e}")
    
    def force_complete_analysis(self):
        """Força análise completa de todos os pares"""
        try:
            logger.info("🔍 Forçando análise completa...")
            
            if not self.analyzer:
                logger.warning("Sistema de análise não disponível")
                return
            
            # Inicia análise se não estiver rodando
            if not self.analyzer.running:
                self.analyzer.start_analysis()
                logger.info("🚀 Sistema de análise iniciado")
            
            # Força análise completa
            self.analyzer.force_full_analysis()
            
            # Atualiza status
            if 'analysis_status' in self.tags:
                dpg.set_value(self.tags['analysis_status'], "🔄 Executando análise completa...")
            
            logger.info("✅ Análise completa iniciada")
            
        except Exception as e:
            logger.error(f"Erro ao forçar análise completa: {e}")
    
    def export_pairs_table(self):
        """Exporta tabela de pares para arquivo CSV"""
        try:
            logger.info("📋 Exportando tabela de pares...")
            
            import csv
            from pathlib import Path
            
            # Cria diretório de exports
            export_dir = Path("exports")
            export_dir.mkdir(exist_ok=True)
            
            # Nome do arquivo
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            export_file = export_dir / f"bancomat4_pairs_analysis_{timestamp}.csv"
            
            # Exporta dados
            with open(export_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                
                # Cabeçalho
                writer.writerow([
                    'Par', 'Score', 'Status', 'Z-Score', 'Correlação', 
                    'Cointegração', 'Half-Life', 'Beta', 'Confiança', 'Última Análise'
                ])
                
                # Dados
                for pair_data in self.pairs_analysis_data:
                    writer.writerow([
                        pair_data['pair_name'],
                        f"{pair_data['score']:.1f}",
                        pair_data['status'],
                        f"{pair_data['z_score']:.3f}",
                        f"{pair_data['correlation']:.3f}",
                        f"{pair_data['cointegration_pvalue']:.4f}",
                        f"{pair_data['half_life']:.1f}",
                        f"{pair_data['beta']:.3f}",
                        f"{pair_data['confidence']:.1%}",
                        pair_data['last_analysis']
                    ])
            
            logger.info(f"✅ Tabela exportada: {export_file}")
            
            # Atualiza logs na interface
            if 'logs_text' in self.tags:
                timestamp_ui = datetime.now().strftime("%H:%M:%S")
                log_entry = f"{timestamp_ui} - Tabela de pares exportada para {export_file.name}\n"
                current_logs = dpg.get_value(self.tags['logs_text'])
                dpg.set_value(self.tags['logs_text'], current_logs + log_entry)
            
        except Exception as e:
            logger.error(f"Erro ao exportar tabela: {e}")
    
    def run(self):
        """Executa a interface gráfica"""
        try:
            logger.info("🚀 Iniciando execução da interface...")
            
            # Cria interface
            logger.info("🏗️ Criando interface...")
            self.create_interface()
            
            # Configura e exibe DearPyGui
            logger.info("🔧 Configurando DearPyGui...")
            dpg.setup_dearpygui()
            
            logger.info("✅ DearPyGui configurado")
            
            # Exibe interface
            logger.info("👁️ Exibindo interface...")
            dpg.show_viewport()
            logger.info("✅ Interface exibida")
            
            # Loop principal
            logger.info("🔄 Iniciando loop principal...")
            dpg.start_dearpygui()
            logger.info("🛑 Loop principal finalizado")
            
            # Para serviços em background
            logger.info("🛑 Parando serviços...")
            self.running = False
            
            # Para analyzer se estiver rodando
            if hasattr(self, 'analyzer') and self.analyzer and hasattr(self.analyzer, 'running'):
                if self.analyzer.running:
                    self.analyzer.stop_analysis()
            
            # Para auto trader se estiver rodando
            if hasattr(self, 'auto_trader') and self.auto_trader and hasattr(self.auto_trader, 'running'):
                if self.auto_trader.running:
                    self.auto_trader.stop_trading()
            
            # Limpa recursos do DearPyGui
            logger.info("🧹 Limpando recursos...")
            dpg.destroy_context()
            logger.info("✅ Recursos limpos")
            
        except Exception as e:
            logger.error(f"❌ Erro na execução da interface: {e}")
            import traceback
            logger.error(f"📋 Traceback: {traceback.format_exc()}")
            
            # Garante limpeza mesmo em caso de erro
            try:
                self.running = False
                if dpg.is_dearpygui_running():
                    dpg.stop_dearpygui()
                dpg.destroy_context()
            except Exception as cleanup_error:
                logger.error(f"Erro na limpeza: {cleanup_error}")
            
            raise
    
    def update_pairs_list(self):
        """Atualiza lista simples de pares (compatibilidade)"""
        try:
            if not self.mt5 or not self.mt5.check_connection():
                logger.warning("MT5 não conectado - não é possível atualizar lista de pares")
                return
            
            # Obtém pares forex
            forex_pairs = self.mt5.get_forex_pairs()
            
            if not forex_pairs:
                logger.warning("Nenhum par forex disponível")
                return
            
            # Prepara lista para exibição (limitada a 50 para performance)
            pairs_list = []
            for i, pair_info in enumerate(forex_pairs[:50]):
                if hasattr(pair_info, 'name'):
                    pairs_list.append(pair_info.name)
                else:
                    pairs_list.append(str(pair_info))
            
            # Atualiza listbox se existir (compatibilidade com versão anterior)
            if dpg.does_item_exist("pairs_listbox"):
                dpg.configure_item("pairs_listbox", items=pairs_list)
            
            logger.info(f"Lista de pares atualizada: {len(pairs_list)} pares")
            
        except Exception as e:
            logger.error(f"Erro ao atualizar lista de pares: {e}")
    
    def on_pair_selected(self, sender, app_data):
        """Callback quando um par é selecionado (compatibilidade)"""
        try:
            selected_pair = app_data
            logger.debug(f"Par selecionado: {selected_pair}")
            
            # Atualiza informações do par selecionado se os tags existirem
            if 'selected_pair_details' in self.tags:
                details = f"Par selecionado: {selected_pair}\n"
                details += "Clique em 'Analisar Par Selecionado' para ver detalhes completos."
                dpg.set_value(self.tags['selected_pair_details'], details)
                
        except Exception as e:
            logger.error(f"Erro ao processar seleção do par: {e}")
    
    def open_trade_from_pair(self):
        """Abre trade a partir do par selecionado (placeholder)"""
        logger.info("Função 'Abrir Trade' será implementada futuramente")
        
        # Atualiza logs na interface
        if 'logs_text' in self.tags:
            timestamp = datetime.now().strftime("%H:%M:%S")
            log_entry = f"{timestamp} - Funcionalidade 'Abrir Trade' em desenvolvimento\n"
            current_logs = dpg.get_value(self.tags['logs_text'])
            dpg.set_value(self.tags['logs_text'], current_logs + log_entry)
    

    def apply_pairs_filter(self):
        """Aplica filtros na tabela de pares e atualiza exibição"""
        try:
            logger.debug("🔍 Aplicando filtros nos pares...")
            
            # Atualiza a tabela com os filtros aplicados
            self.update_pairs_table()
            
            # Atualiza estatísticas baseadas nos filtros
            if not hasattr(self, 'pairs_analysis_data'):
                return
            
            # Obtém filtros atuais com verificação de existência
            show_approved_only = False
            show_signals_only = False
            min_score = 0.0
            
            try:
                if dpg.does_item_exist("filter_approved_only"):
                    show_approved_only = dpg.get_value("filter_approved_only")
            except:
                pass
                
            try:
                if dpg.does_item_exist("filter_with_signals"):
                    show_signals_only = dpg.get_value("filter_with_signals")
            except:
                pass
                
            try:
                if dpg.does_item_exist("filter_min_score"):
                    min_score = dpg.get_value("filter_min_score")
            except:
                pass
            
            # Conta pares filtrados
            filtered_count = 0
            for pair_data in self.pairs_analysis_data:
                # Aplica os mesmos filtros da tabela
                if show_approved_only and pair_data['status'] not in ['APPROVED', 'BUY_SIGNAL', 'SELL_SIGNAL']:
                    continue
                
                if show_signals_only and pair_data['status'] not in ['BUY_SIGNAL', 'SELL_SIGNAL']:
                    continue
                
                if pair_data['score'] < min_score:
                    continue
                
                filtered_count += 1
            
            # Atualiza status de filtros
            if 'pairs_analysis_status' in self.tags:
                status_text = f"📊 {filtered_count} pares"
                if show_approved_only:
                    status_text += " (apenas aprovados)"
                if show_signals_only:
                    status_text += " (apenas com sinais)"
                if min_score > 0:
                    status_text += f" (score >= {min_score:.1f})"
                
                dpg.set_value(self.tags['pairs_analysis_status'], status_text)
            
            logger.debug(f"✅ Filtros aplicados - {filtered_count} pares exibidos")
            
        except Exception as e:
            logger.error(f"Erro ao aplicar filtros de pares: {e}")

    def save_max_trades_config(self):
        """Salva configuração de max trades da interface"""
        try:
            if dpg.does_item_exist("max_trades"):
                max_trades = dpg.get_value("max_trades")
                
                # Valida valor
                if 1 <= max_trades <= 20:
                    self.config.set('trading.max_simultaneous_trades', max_trades)
                    self.config.save_user_config()
                    
                    logger.info(f"✅ Max trades atualizado para: {max_trades}")
                    
                    if "trading_status" in self.tags:
                        dpg.set_value(self.tags["trading_status"], 
                                    f"Status: Max {max_trades} trades")
                else:
                    logger.warning(f"⚠️ Valor inválido para max trades: {max_trades}")
                    
        except Exception as e:
            logger.error(f"❌ Erro ao salvar max trades: {e}")

    def initialize_auto_trader(self):
        """Inicializa o AutoTrader se disponível"""
        try:
            if not hasattr(self, 'auto_trader') or self.auto_trader is None:
                # Tenta importar e inicializar AutoTrader
                try:
                    from core.trader import AutoTrader
                    from core.analyzer import PairAnalyzer
                    
                    # Inicializa analyzer se não existe
                    if not hasattr(self, 'analyzer') or self.analyzer is None:
                        self.analyzer = PairAnalyzer(self.config, self.mt5)
                    
                    # Inicializa AutoTrader
                    self.auto_trader = AutoTrader(self.config, self.mt5, self.analyzer)
                    logger.info("✅ AutoTrader inicializado com sucesso")
                    
                    if 'trading_status' in self.tags:
                        dpg.set_value(self.tags['trading_status'], "Status: Sistema Pronto")
                        
                except ImportError as e:
                    logger.warning(f"⚠️ Não foi possível importar AutoTrader: {e}")
                    self.auto_trader = None
                except Exception as e:
                    logger.error(f"❌ Erro ao inicializar AutoTrader: {e}")
                    self.auto_trader = None
        except Exception as e:
            logger.error(f"❌ Erro na inicialização do AutoTrader: {e}")
            self.auto_trader = None

    def save_settings(self):
        """Salva configurações da interface"""
        try:
            logger.info("💾 Salvando configurações da interface...")
            
            # Por enquanto, apenas salva a configuração atual
            if self.config:
                self.config.save_user_config()
                logger.info("✅ Configurações salvas com sucesso")
                
                # Atualiza logs na interface
                if 'logs_text' in self.tags:
                    timestamp = datetime.now().strftime("%H:%M:%S")
                    log_entry = f"{timestamp} - Configurações da interface salvas\n"
                    current_logs = dpg.get_value(self.tags['logs_text'])
                    dpg.set_value(self.tags['logs_text'], current_logs + log_entry)
            else:
                logger.warning("⚠️ Gerenciador de configuração não disponível")
                
        except Exception as e:
            logger.error(f"Erro ao salvar configurações: {e}")
            
            # Atualiza logs na interface
            if 'logs_text' in self.tags:
                timestamp = datetime.now().strftime("%H:%M:%S")
                log_entry = f"{timestamp} - Erro ao salvar configurações: {str(e)}\n"
                current_logs = dpg.get_value(self.tags['logs_text'])
                dpg.set_value(self.tags['logs_text'], current_logs + log_entry)