#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Correção dos Problemas de Trading - Bancomat 4
- Corrige atualização do current_zscore
- Melhora controle de max_simultaneous_trades
- Adiciona logging detalhado
"""

import json
import sys
from pathlib import Path
from datetime import datetime

# Adiciona o diretório do projeto ao path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from utils.config import ConfigManager
from utils.logger import get_logger

# Configurar logger
logger = get_logger(__name__)

def update_auto_trader_with_zscore_fix():
    """Atualiza o AutoTrader para corrigir o problema do current_zscore"""
    
    print("🔧 CORRIGINDO ATUALIZAÇÃO DO CURRENT Z-SCORE")
    print("=" * 60)
    
    auto_trader_file = project_root / "core" / "trader.py"
    
    # Lê o arquivo atual
    with open(auto_trader_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Adiciona método de atualização de Z-Score
    zscore_update_method = '''
    def _update_current_zscores(self):
        """Atualiza o current_zscore de todos os trades ativos"""
        try:
            for trade_id, trade in self.active_trades.items():
                try:
                    # Calcula Z-Score atual
                    current_analysis = self.analyzer.analyze_pair(
                        trade.pair_name,
                        timeframe=trade.timeframe
                    )
                    
                    if current_analysis and 'z_score' in current_analysis:
                        old_zscore = trade.current_zscore
                        trade.current_zscore = current_analysis['z_score']
                        
                        # Log apenas se mudou significativamente
                        if abs(old_zscore - trade.current_zscore) > 0.01:
                            logger.debug(f"Z-Score atualizado para {trade.pair_name}: "
                                       f"{old_zscore:.3f} → {trade.current_zscore:.3f}")
                        
                        # Atualiza timestamp da última atualização
                        trade.last_update = datetime.now()
                        
                except Exception as e:
                    logger.error(f"Erro ao atualizar Z-Score do trade {trade_id}: {e}")
                    
        except Exception as e:
            logger.error(f"Erro ao atualizar Z-Scores: {e}")
'''
    
    # Adiciona chamada no loop principal
    loop_update = '''
            # Atualiza Z-Scores dos trades ativos (a cada 30 segundos)
            if time.time() - getattr(self, '_last_zscore_update', 0) > 30:
                self._update_current_zscores()
                self._last_zscore_update = time.time()
'''
    
    # Procura onde inserir
    if '_update_current_zscores' not in content:
        # Insere o método antes do último método da classe
        insertion_point = content.rfind('\n    def ')
        if insertion_point != -1:
            new_content = (content[:insertion_point] + 
                          zscore_update_method + 
                          content[insertion_point:])
            
            # Adiciona a chamada no loop principal
            if 'while self.running' in new_content:
                loop_pos = new_content.find('while self.running')
                loop_end = new_content.find('\n                time.sleep', loop_pos)
                if loop_end != -1:
                    new_content = (new_content[:loop_end] + 
                                  loop_update + 
                                  new_content[loop_end:])
            
            # Salva o arquivo atualizado
            with open(auto_trader_file, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            print("✅ Método _update_current_zscores adicionado")
            print("✅ Chamada no loop principal adicionada")
        else:
            print("❌ Não foi possível encontrar local para inserir o método")
    else:
        print("✅ Método _update_current_zscores já existe")

def enhance_max_trades_control():
    """Melhora o controle de max_simultaneous_trades"""
    
    print("\n🔧 MELHORANDO CONTROLE DE MAX TRADES")
    print("=" * 60)
    
    auto_trader_file = project_root / "core" / "trader.py"
    
    # Lê o arquivo atual
    with open(auto_trader_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Melhora o método _should_execute_signal
    enhanced_check = '''
            # Verifica número máximo de trades com logging detalhado
            max_trades = self.trading_config.get('max_simultaneous_trades', 5)
            current_count = len(self.active_trades)
            
            if current_count >= max_trades:
                logger.info(f"🚫 Limite de trades atingido: {current_count}/{max_trades}")
                logger.debug(f"Trades ativos: {[t.pair_name for t in self.active_trades.values()]}")
                return False
            else:
                logger.debug(f"✅ Pode abrir trade: {current_count}/{max_trades}")
'''
    
    # Substitui a verificação existente
    if 'len(self.active_trades) >= max_trades' in content:
        # Encontra e substitui o bloco
        start_pos = content.find('# Verifica número máximo de trades')
        if start_pos == -1:
            start_pos = content.find('max_trades = self.trading_config.get(\'max_simultaneous_trades\'')
        
        if start_pos != -1:
            end_pos = content.find('return False', start_pos)
            if end_pos != -1:
                end_pos = content.find('\n', end_pos) + 1
                
                new_content = (content[:start_pos] + 
                              enhanced_check + 
                              content[end_pos:])
                
                # Salva o arquivo atualizado
                with open(auto_trader_file, 'w', encoding='utf-8') as f:
                    f.write(new_content)
                
                print("✅ Controle de max trades melhorado com logging")
            else:
                print("❌ Não foi possível encontrar o final do bloco")
        else:
            print("❌ Não foi possível encontrar o bloco de verificação")
    else:
        print("✅ Controle de max trades já está implementado")

def fix_interface_config_saving():
    """Corrige salvamento da configuração na interface"""
    
    print("\n🔧 CORRIGINDO SALVAMENTO NA INTERFACE")
    print("=" * 60)
    
    interface_file = project_root / "ui" / "advanced_interface.py"
    
    # Lê o arquivo atual
    with open(interface_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Adiciona método para salvar configuração de max trades
    save_method = '''
    def save_max_trades_config(self):
        """Salva configuração de max trades da interface"""
        try:
            if dpg.does_item_exist("max_trades"):
                max_trades = dpg.get_value("max_trades")
                
                # Valida valor
                if 1 <= max_trades <= 20:
                    self.config.set('trading.max_simultaneous_trades', max_trades)
                    self.config.save_user_config()
                    
                    logger.info(f"✅ Max trades atualizado para: {max_trades}")
                    
                    if "trading_status" in self.tags:
                        dpg.set_value(self.tags["trading_status"], 
                                    f"Status: Max {max_trades} trades")
                else:
                    logger.warning(f"⚠️ Valor inválido para max trades: {max_trades}")
                    
        except Exception as e:
            logger.error(f"❌ Erro ao salvar max trades: {e}")
'''
    
    # Adiciona callback ao campo max_trades
    callback_addition = '''
                    dpg.add_input_int(label="Max Trades", default_value=5, tag="max_trades", 
                                     width=200, callback=self.save_max_trades_config)
'''
    
    # Procura e substitui o campo max_trades
    if 'tag="max_trades"' in content and 'callback=' not in content[content.find('tag="max_trades"')-100:content.find('tag="max_trades"')+100]:
        # Adiciona o método
        if 'save_max_trades_config' not in content:
            # Insere antes do último método
            insertion_point = content.rfind('\n    def ')
            if insertion_point != -1:
                new_content = (content[:insertion_point] + 
                              save_method + 
                              content[insertion_point:])
                
                # Adiciona callback ao campo
                old_field = 'dpg.add_input_int(label="Max Trades", default_value=5, tag="max_trades", width=200)'
                new_field = 'dpg.add_input_int(label="Max Trades", default_value=5, tag="max_trades", width=200, callback=self.save_max_trades_config)'
                
                new_content = new_content.replace(old_field, new_field)
                
                # Salva o arquivo atualizado
                with open(interface_file, 'w', encoding='utf-8') as f:
                    f.write(new_content)
                
                print("✅ Método save_max_trades_config adicionado")
                print("✅ Callback adicionado ao campo Max Trades")
            else:
                print("❌ Não foi possível encontrar local para inserir o método")
        else:
            print("✅ Método save_max_trades_config já existe")
    else:
        print("✅ Campo Max Trades já tem callback ou não foi encontrado")

def create_trade_status_monitor():
    """Cria monitor de status dos trades"""
    
    print("\n🔧 CRIANDO MONITOR DE STATUS DOS TRADES")
    print("=" * 60)
    
    monitor_code = '''#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Monitor de Status dos Trades - Bancomat 4
- Monitora atualização de Z-Scores
- Verifica controle de max trades
- Gera alertas para problemas
"""

import json
import time
from pathlib import Path
from datetime import datetime, timedelta

class TradeStatusMonitor:
    """Monitor de status dos trades"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.trades_file = self.project_root / "data" / "bancomat4_trades.json"
    
    def check_zscore_updates(self):
        """Verifica se Z-Scores estão sendo atualizados"""
        if not self.trades_file.exists():
            return []
        
        with open(self.trades_file, 'r') as f:
            trades = json.load(f)
        
        issues = []
        for trade in trades:
            entry_zscore = trade.get('entry_zscore', 0)
            current_zscore = trade.get('current_zscore', 0)
            
            if abs(entry_zscore - current_zscore) < 0.000001:
                entry_time = datetime.fromtimestamp(trade['entry_time'])
                age_minutes = (datetime.now() - entry_time).total_seconds() / 60
                
                if age_minutes > 5:  # Trade com mais de 5 minutos
                    issues.append({
                        'pair': trade['pair_name'],
                        'age_minutes': age_minutes,
                        'issue': 'Z-Score não atualizado'
                    })
        
        return issues
    
    def check_max_trades_limit(self, max_trades=5):
        """Verifica se está respeitando o limite de trades"""
        if not self.trades_file.exists():
            return None
        
        with open(self.trades_file, 'r') as f:
            trades = json.load(f)
        
        current_count = len(trades)
        
        return {
            'current_count': current_count,
            'max_trades': max_trades,
            'within_limit': current_count <= max_trades,
            'trades': [t['pair_name'] for t in trades]
        }
    
    def generate_report(self):
        """Gera relatório de status"""
        print("📊 RELATÓRIO DE STATUS DOS TRADES")
        print("=" * 50)
        
        # Verifica Z-Scores
        zscore_issues = self.check_zscore_updates()
        if zscore_issues:
            print("⚠️ PROBLEMAS COM Z-SCORE:")
            for issue in zscore_issues:
                print(f"   • {issue['pair']}: {issue['issue']} ({issue['age_minutes']:.1f} min)")
        else:
            print("✅ Z-Scores estão sendo atualizados corretamente")
        
        # Verifica limite de trades
        max_trades_status = self.check_max_trades_limit()
        if max_trades_status:
            print(f"\n📈 CONTROLE DE TRADES:")
            print(f"   • Trades ativos: {max_trades_status['current_count']}")
            print(f"   • Limite máximo: {max_trades_status['max_trades']}")
            if max_trades_status['within_limit']:
                print("   ✅ Dentro do limite")
            else:
                print("   ⚠️ ACIMA DO LIMITE!")
            print(f"   • Pares ativos: {', '.join(max_trades_status['trades'])}")

if __name__ == "__main__":
    monitor = TradeStatusMonitor()
    monitor.generate_report()
'''
    
    monitor_file = project_root / "monitor_trades.py"
    with open(monitor_file, 'w', encoding='utf-8') as f:
        f.write(monitor_code)
    
    print(f"✅ Monitor criado: {monitor_file}")

def main():
    """Executa todas as correções"""
    
    print("🔧 CORREÇÃO DOS PROBLEMAS DE TRADING")
    print("🎯 Corrigindo atualização de Z-Score e controle de trades")
    print("=" * 70)
    
    # Corrige atualização do Z-Score
    update_auto_trader_with_zscore_fix()
    
    # Melhora controle de max trades
    enhance_max_trades_control()
    
    # Corrige salvamento na interface
    fix_interface_config_saving()
    
    # Cria monitor
    create_trade_status_monitor()
    
    print(f"\n✅ CORREÇÕES APLICADAS COM SUCESSO!")
    print(f"📋 PRÓXIMOS PASSOS:")
    print(f"   1. Reiniciar o sistema para aplicar as correções")
    print(f"   2. Executar: python monitor_trades.py (para monitorar)")
    print(f"   3. Verificar logs para confirmação das correções")
    print(f"   4. Testar configuração de max trades na interface")

if __name__ == "__main__":
    main() 