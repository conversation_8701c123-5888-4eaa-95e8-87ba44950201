#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Conector MT5 otimizado para Bancomat 4
- Conexão robusta com retry automático
- Cache de dados para performance
- Busca automática de pares forex
- Monitoramento de margem e posições
"""

import MetaTrader5 as mt5
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
import threading
import time
import fnmatch
from dataclasses import dataclass

from utils.logger import get_logger
from utils.config import ConfigManager

logger = get_logger(__name__)


@dataclass
class SymbolInfo:
    """Informações de um símbolo"""
    name: str
    description: str
    currency_base: str
    currency_profit: str
    volume_min: float
    volume_max: float
    volume_step: float
    spread: float
    trade_mode: int
    is_forex: bool
    

@dataclass
class AccountInfo:
    """Informações da conta"""
    login: int
    balance: float
    equity: float
    margin: float
    margin_free: float
    margin_level: float
    currency: str
    server: str
    

@dataclass
class Position:
    """Informação de uma posição"""
    ticket: int
    symbol: str
    type: int
    volume: float
    price_open: float
    price_current: float
    profit: float
    swap: float
    commission: float
    comment: str
    magic: int
    time_open: datetime


class MT5Connector:
    """Conector otimizado para MetaTrader 5"""
    
    def __init__(self, config: ConfigManager):
        self.config = config
        self.connected = False
        self.account_info = None
        self.forex_pairs = []
        self.forex_pairs_last_scan = 0
        self.forex_pairs_scan_interval = 3600  # 1 hora fixo
        
        # Cache e threading
        self._cache = {}
        self._cache_expiry = {}
        self._cache_lock = threading.RLock()
        
        self.is_connected = False
        self.last_connection_check = 0
        self.connection_check_interval = 30  # segundos
        
        # Informações da conta
        self.account_info_last_update = 0
        
        # Lock para operações
        self.operation_lock = threading.RLock()
        
        logger.info("🔗 MT5Connector inicializado")
        
    def connect(self) -> bool:
        """
        Conecta ao MT5
        
        Returns:
            True se conectado com sucesso
        """
        with self.operation_lock:
            try:
                # Obtém configurações de conexão
                terminal_path = self.config.get('mt5.terminal_path')
                login = self.config.get('mt5.login', 0)
                password = self.config.get('mt5.password', '')
                server = self.config.get('mt5.server', '')
                
                logger.info(f"Conectando ao MetaTrader 5 - Terminal: {terminal_path}")
                
                # Inicializa MT5 com o terminal específico
                if terminal_path:
                    if not mt5.initialize(terminal_path):
                        error = mt5.last_error()
                        logger.error(f"Falha ao inicializar MT5 com terminal {terminal_path}: {error}")
                        return False
                    logger.info(f"✅ MT5 inicializado com terminal específico: {terminal_path}")
                else:
                    if not mt5.initialize():
                        logger.error("Falha ao inicializar MT5")
                        return False
                    logger.warning("⚠️ MT5 inicializado sem terminal específico")
                
                # Se tem configurações de login, tenta conectar
                if login and password and server:
                    logger.info(f"Conectando com login {login} no servidor {server}")
                    
                    if not mt5.login(login, password, server):
                        error = mt5.last_error()
                        logger.error(f"Falha ao fazer login: {error}")
                        return False
                
                # Verifica se está conectado
                account_info = mt5.account_info()
                if account_info is None:
                    logger.error("Falha ao obter informações da conta")
                    return False
                
                self.is_connected = True
                self.last_connection_check = time.time()
                
                logger.info(f"✅ Conectado ao MT5 - Conta: {account_info.login}, "
                           f"Servidor: {account_info.server}, Saldo: {account_info.balance}")
                
                # Carrega informações iniciais
                self._update_account_info()
                self._scan_forex_pairs()
                
                return True
                
            except Exception as e:
                logger.error(f"Erro ao conectar ao MT5: {e}")
                self.is_connected = False
                return False
    
    def disconnect(self):
        """Desconecta do MT5"""
        try:
            mt5.shutdown()
            self.is_connected = False
            logger.info("Desconectado do MT5")
        except Exception as e:
            logger.error(f"Erro ao desconectar: {e}")
    
    def check_connection(self) -> bool:
        """
        Verifica se a conexão está ativa
        
        Returns:
            True se conectado
        """
        current_time = time.time()
        
        # Verifica periodicamente
        if current_time - self.last_connection_check > self.connection_check_interval:
            try:
                account_info = mt5.account_info()
                self.is_connected = account_info is not None
                self.last_connection_check = current_time
                
                if not self.is_connected:
                    logger.warning("Conexão MT5 perdida")
                
            except Exception as e:
                logger.error(f"Erro ao verificar conexão: {e}")
                self.is_connected = False
        
        return self.is_connected
    
    def ensure_connection(self) -> bool:
        """
        Garante que está conectado, reconectando se necessário
        
        Returns:
            True se conectado
        """
        if not self.check_connection():
            logger.info("Tentando reconectar ao MT5...")
            return self.connect()
        return True
    
    def _update_account_info(self):
        """Atualiza informações da conta"""
        try:
            if not self.ensure_connection():
                return
            
            account_info = mt5.account_info()
            if account_info:
                self.account_info = AccountInfo(
                    login=account_info.login,
                    balance=account_info.balance,
                    equity=account_info.equity,
                    margin=account_info.margin,
                    margin_free=account_info.margin_free,
                    margin_level=account_info.margin_level,
                    currency=account_info.currency,
                    server=account_info.server
                )
                self.account_info_last_update = time.time()
                
        except Exception as e:
            logger.error(f"Erro ao atualizar informações da conta: {e}")
    
    def get_account_info(self, force_update: bool = False) -> Optional[AccountInfo]:
        """
        Obtém informações da conta
        
        Args:
            force_update: Força atualização dos dados
            
        Returns:
            Informações da conta ou None
        """
        current_time = time.time()
        
        # Atualiza se necessário
        if (force_update or 
            self.account_info is None or 
            current_time - self.account_info_last_update > 30):
            self._update_account_info()
        
        return self.account_info
    
    def _scan_forex_pairs(self):
        """Busca pares forex disponíveis no servidor"""
        try:
            if not self.ensure_connection():
                return
            
            logger.info("Buscando pares forex disponíveis...")
            
            # Obtém todos os símbolos
            symbols = mt5.symbols_get()
            if not symbols:
                logger.warning("Nenhum símbolo encontrado")
                return
            
            forex_pairs = []
            
            for symbol in symbols:
                try:
                    # Verifica se é forex
                    if not self._is_forex_symbol(symbol):
                        continue
                    
                    # Adiciona à lista
                    symbol_info = SymbolInfo(
                        name=symbol.name,
                        description=symbol.description,
                        currency_base=symbol.currency_base,
                        currency_profit=symbol.currency_profit,
                        volume_min=symbol.volume_min,
                        volume_max=symbol.volume_max,
                        volume_step=symbol.volume_step,
                        spread=0,  # Spread será calculado
                        trade_mode=symbol.trade_mode,
                        is_forex=True
                    )
                    
                    forex_pairs.append(symbol_info)
                    
                except Exception as e:
                    logger.debug(f"Erro ao processar símbolo {symbol.name}: {e}")
                    continue
            
            self.forex_pairs = forex_pairs
            self.forex_pairs_last_scan = time.time()
            
            logger.info(f"✅ Encontrados {len(forex_pairs)} pares forex válidos")
            
            # Log dos primeiros 10 pares para debug
            for pair in forex_pairs[:10]:
                logger.debug(f"Par: {pair.name} - Spread: {pair.spread:.1f} - "
                           f"Vol.Min: {pair.volume_min}")
            
        except Exception as e:
            logger.error(f"Erro ao buscar pares forex: {e}")
    
    def _is_forex_symbol(self, symbol) -> bool:
        """
        Verifica se um símbolo é forex
        
        Args:
            symbol: Símbolo do MT5
            
        Returns:
            True se é forex
        """
        try:
            # Verifica se tem moedas base e lucro diferentes
            if not symbol.currency_base or not symbol.currency_profit:
                return False
            
            # Verifica se são moedas conhecidas
            forex_currencies = {
                'USD', 'EUR', 'GBP', 'JPY', 'CHF', 'CAD', 'AUD', 'NZD',
                'SEK', 'NOK', 'DKK', 'PLN', 'CZK', 'HUF', 'TRY', 'ZAR',
                'MXN', 'SGD', 'HKD'
            }
            
            if (symbol.currency_base in forex_currencies and 
                symbol.currency_profit in forex_currencies):
                return True
            
            # Verifica padrões de nome
            name = symbol.name.upper()
            
            # Pares de 6 caracteres sem separador
            if len(name) == 6 and name[:3] != name[3:]:
                return True
            
            # Pares com separadores
            if any(sep in name for sep in ['/', '.', '_']):
                parts = name.replace('/', '').replace('.', '').replace('_', '')
                if len(parts) == 6:
                    return True
            
            return False
            
        except Exception:
            return False
    
    def get_forex_pairs(self, force_scan: bool = False) -> List[SymbolInfo]:
        """
        Obtém lista de pares forex
        
        Args:
            force_scan: Força nova busca
            
        Returns:
            Lista de pares forex
        """
        current_time = time.time()
        
        # Faz nova busca se necessário
        if (force_scan or 
            not self.forex_pairs or 
            current_time - self.forex_pairs_last_scan > self.forex_pairs_scan_interval):
            self._scan_forex_pairs()
        
        return self.forex_pairs.copy()
    
    def get_symbol_info(self, symbol: str) -> Optional[Dict[str, Any]]:
        """
        Obtém informações de um símbolo
        
        Args:
            symbol: Nome do símbolo
            
        Returns:
            Informações do símbolo ou None
        """
        try:
            if not self.ensure_connection():
                return None
            
            info = mt5.symbol_info(symbol)
            if info is None:
                return None
            
            return {
                'name': info.name,
                'description': info.description,
                'currency_base': info.currency_base,
                'currency_profit': info.currency_profit,
                'volume_min': info.volume_min,
                'volume_max': info.volume_max,
                'volume_step': info.volume_step,
                'point': info.point,
                'digits': info.digits,
                'spread': info.spread,
                'trade_mode': info.trade_mode,
                'trade_tick_value': getattr(info, 'trade_tick_value', 1.0),
                'trade_tick_size': getattr(info, 'trade_tick_size', info.point)
            }
            
        except Exception as e:
            logger.error(f"Erro ao obter informações do símbolo {symbol}: {e}")
            return None
    
    def get_tick_info(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Obtém informações do tick atual"""
        try:
            if not self.check_connection():
                return None
            
            tick = mt5.symbol_info_tick(symbol)
            if tick is None:
                return None
            
            return {
                'symbol': symbol,
                'time': tick.time,
                'bid': tick.bid,
                'ask': tick.ask,
                'last': tick.last,
                'volume': tick.volume
            }
            
        except Exception as e:
            logger.error(f"Erro ao obter tick de {symbol}: {e}")
            return None
    
    def get_price_data(self, symbol: str, timeframe: int, count: int) -> Optional[pd.DataFrame]:
        """
        Obtém dados de preço
        
        Args:
            symbol: Nome do símbolo
            timeframe: Timeframe em minutos
            count: Número de candles
            
        Returns:
            DataFrame com dados ou None
        """
        try:
            if not self.ensure_connection():
                return None
            
            # Cache key
            cache_key = f"price_data_{symbol}_{timeframe}_{count}"
            cache_duration = self.config.get('analysis.cache_duration', 300)
            
            # Verifica cache
            with self._cache_lock:
                if (cache_key in self._cache and 
                    cache_key in self._cache_expiry and
                    time.time() < self._cache_expiry[cache_key]):
                    return self._cache[cache_key].copy()
            
            # Converte timeframe para MT5
            mt5_timeframe = self._convert_timeframe(timeframe)
            if mt5_timeframe is None:
                return None
            
            # Obtém dados
            rates = mt5.copy_rates_from_pos(symbol, mt5_timeframe, 0, count)
            if rates is None or len(rates) == 0:
                logger.warning(f"Nenhum dado obtido para {symbol} TF{timeframe}")
                return None
            
            # Converte para DataFrame
            df = pd.DataFrame(rates)
            df['time'] = pd.to_datetime(df['time'], unit='s')
            df.set_index('time', inplace=True)
            
            # Armazena no cache
            with self._cache_lock:
                self._cache[cache_key] = df.copy()
                self._cache_expiry[cache_key] = time.time() + cache_duration
            
            return df
            
        except Exception as e:
            logger.error(f"Erro ao obter dados de preço para {symbol}: {e}")
            return None
    
    def _convert_timeframe(self, minutes: int) -> Optional[int]:
        """
        Converte timeframe em minutos para constante MT5
        
        Args:
            minutes: Timeframe em minutos
            
        Returns:
            Constante MT5 ou None
        """
        timeframes = {
            1: mt5.TIMEFRAME_M1,
            5: mt5.TIMEFRAME_M5,
            15: mt5.TIMEFRAME_M15,
            30: mt5.TIMEFRAME_M30,
            60: mt5.TIMEFRAME_H1,
            240: mt5.TIMEFRAME_H4,
            1440: mt5.TIMEFRAME_D1
        }
        
        return timeframes.get(minutes)
    
    def get_positions(self) -> List[Position]:
        """
        Obtém posições abertas
        
        Returns:
            Lista de posições
        """
        try:
            if not self.ensure_connection():
                return []
            
            positions = mt5.positions_get()
            if not positions:
                return []
            
            result = []
            for pos in positions:
                position = Position(
                    ticket=pos.ticket,
                    symbol=pos.symbol,
                    type=pos.type,
                    volume=pos.volume,
                    price_open=pos.price_open,
                    price_current=pos.price_current,
                    profit=pos.profit,
                    swap=pos.swap,
                    commission=pos.commission,
                    comment=pos.comment,
                    magic=pos.magic,
                    time_open=datetime.fromtimestamp(pos.time)
                )
                result.append(position)
            
            return result
            
        except Exception as e:
            logger.error(f"Erro ao obter posições: {e}")
            return []
    
    def open_position(self, symbol: str, order_type: str, volume: float, 
                     comment: str = "", magic: int = 0) -> Dict[str, Any]:
        """
        Abre uma posição
        
        Args:
            symbol: Símbolo
            order_type: Tipo da ordem (BUY, SELL)
            volume: Volume
            comment: Comentário
            magic: Magic number
            
        Returns:
            Resultado da operação
        """
        try:
            if not self.ensure_connection():
                return {'success': False, 'error': 'Não conectado ao MT5'}
            
            # Determina tipo da ordem
            if order_type.upper() == 'BUY':
                action = mt5.TRADE_ACTION_DEAL
                order_type_mt5 = mt5.ORDER_TYPE_BUY
                price = mt5.symbol_info_tick(symbol).ask
            else:
                action = mt5.TRADE_ACTION_DEAL
                order_type_mt5 = mt5.ORDER_TYPE_SELL
                price = mt5.symbol_info_tick(symbol).bid
            
            if price is None:
                return {'success': False, 'error': f'Preço não disponível para {symbol}'}
            
            # Prepara request
            request = {
                "action": action,
                "symbol": symbol,
                "volume": volume,
                "type": order_type_mt5,
                "price": price,
                "comment": comment,
                "magic": magic,
                "type_filling": mt5.ORDER_FILLING_IOC,
                "type_time": mt5.ORDER_TIME_GTC,
            }
            
            # Envia ordem
            result = mt5.order_send(request)
            
            if result.retcode != mt5.TRADE_RETCODE_DONE:
                error_msg = f"Erro ao abrir posição: {result.retcode} - {result.comment}"
                logger.error(error_msg)
                return {'success': False, 'error': error_msg}
            
            logger.info(f"✅ Posição aberta: {symbol} {order_type} {volume} - Ticket: {result.order}")
            
            return {
                'success': True,
                'ticket': result.order,
                'price': result.price,
                'volume': result.volume
            }
            
        except Exception as e:
            error_msg = f"Erro ao abrir posição: {e}"
            logger.error(error_msg)
            return {'success': False, 'error': error_msg}
    
    def close_position(self, ticket: int) -> Dict[str, Any]:
        """
        Fecha uma posição
        
        Args:
            ticket: Ticket da posição
            
        Returns:
            Resultado da operação
        """
        try:
            if not self.ensure_connection():
                return {'success': False, 'error': 'Não conectado ao MT5'}
            
            # Obtém informações da posição
            positions = mt5.positions_get(ticket=ticket)
            if not positions:
                return {'success': False, 'error': f'Posição {ticket} não encontrada'}
            
            position = positions[0]
            
            # Determina tipo da ordem de fechamento
            if position.type == 0:  # BUY
                order_type = mt5.ORDER_TYPE_SELL
                price = mt5.symbol_info_tick(position.symbol).bid
            else:  # SELL
                order_type = mt5.ORDER_TYPE_BUY
                price = mt5.symbol_info_tick(position.symbol).ask
            
            if price is None:
                return {'success': False, 'error': f'Preço não disponível para {position.symbol}'}
            
            # Prepara request de fechamento
            request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "symbol": position.symbol,
                "volume": position.volume,
                "type": order_type,
                "position": ticket,
                "price": price,
                "comment": f"Close #{ticket}",
                "type_filling": mt5.ORDER_FILLING_IOC,
                "type_time": mt5.ORDER_TIME_GTC,
            }
            
            # Envia ordem de fechamento
            result = mt5.order_send(request)
            
            if result.retcode != mt5.TRADE_RETCODE_DONE:
                error_msg = f"Erro ao fechar posição: {result.retcode} - {result.comment}"
                logger.error(error_msg)
                return {'success': False, 'error': error_msg}
            
            logger.info(f"✅ Posição fechada: Ticket {ticket} - Preço: {result.price}")
            
            return {
                'success': True,
                'ticket': ticket,
                'close_price': result.price,
                'profit': position.profit
            }
            
        except Exception as e:
            error_msg = f"Erro ao fechar posição: {e}"
            logger.error(error_msg)
            return {'success': False, 'error': error_msg}
    
    def is_market_open(self, symbol: str) -> bool:
        """
        Verifica se o mercado está aberto para um símbolo
        
        Args:
            symbol: Nome do símbolo
            
        Returns:
            True se mercado aberto
        """
        try:
            if not self.ensure_connection():
                return False
            
            symbol_info = mt5.symbol_info(symbol)
            if symbol_info is None:
                return False
            
            # Verifica se trading está habilitado
            return symbol_info.trade_mode in [
                mt5.SYMBOL_TRADE_MODE_FULL,
                mt5.SYMBOL_TRADE_MODE_LONGONLY,
                mt5.SYMBOL_TRADE_MODE_SHORTONLY,
                mt5.SYMBOL_TRADE_MODE_CLOSEONLY
            ]
            
        except Exception as e:
            logger.error(f"Erro ao verificar mercado para {symbol}: {e}")
            return False
    
    def check_margin_requirements(self, trades_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Verifica requisitos de margem para trades
        
        Args:
            trades_data: Lista com dados dos trades propostos
            
        Returns:
            Informações sobre margem
        """
        try:
            account_info = self.get_account_info(force_update=True)
            if not account_info:
                return {'success': False, 'error': 'Informações da conta não disponíveis'}
            
            margin_config = self.config.get_section('margin')
            min_free_margin = margin_config.get('min_free_margin', 1000)
            critical_margin_level = margin_config.get('margin_level_critical', 100)
            
            # Calcula margem necessária estimada
            total_estimated_margin = 0
            for trade in trades_data:
                # Estimativa simples baseada no volume
                estimated_margin = trade.get('volume', 0.01) * 1000  # Estimativa rough
                total_estimated_margin += estimated_margin
            
            # Verifica condições
            margin_after = account_info.margin + total_estimated_margin
            free_margin_after = account_info.equity - margin_after
            
            margin_level_after = (account_info.equity / margin_after * 100) if margin_after > 0 else float('inf')
            
            can_open = (
                free_margin_after >= min_free_margin and
                margin_level_after >= critical_margin_level
            )
            
            return {
                'success': True,
                'can_open_trades': can_open,
                'current_margin': account_info.margin,
                'current_free_margin': account_info.margin_free,
                'current_margin_level': account_info.margin_level,
                'estimated_margin_after': margin_after,
                'estimated_free_margin_after': free_margin_after,
                'estimated_margin_level_after': margin_level_after,
                'min_free_margin_required': min_free_margin,
                'critical_margin_level': critical_margin_level
            }
            
        except Exception as e:
            logger.error(f"Erro ao verificar margem: {e}")
            return {'success': False, 'error': str(e)}
    
    def cleanup_cache(self):
        """Limpa cache expirado"""
        try:
            with self._cache_lock:
                current_time = time.time()
                expired_keys = [
                    key for key, expiry_time in self._cache_expiry.items()
                    if current_time > expiry_time
                ]
                
                for key in expired_keys:
                    self._cache.pop(key, None)
                    self._cache_expiry.pop(key, None)
                
                if expired_keys:
                    logger.debug(f"Cache limpo: {len(expired_keys)} itens removidos")
                    
        except Exception as e:
            logger.error(f"Erro ao limpar cache: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Obtém estatísticas do conector
        
        Returns:
            Estatísticas
        """
        return {
            'connected': self.is_connected,
            'forex_pairs_count': len(self.forex_pairs),
            'cache_items': len(self._cache),
            'last_connection_check': self.last_connection_check,
            'last_pairs_scan': self.forex_pairs_last_scan
        }
    
    def get_open_positions(self) -> List[Dict[str, Any]]:
        """Obtém lista de posições abertas"""
        try:
            if not self.check_connection():
                return []
            
            positions = mt5.positions_get()
            if positions is None:
                return []
            
            position_list = []
            for pos in positions:
                position_dict = {
                    'ticket': pos.ticket,
                    'time': pos.time,
                    'symbol': pos.symbol,
                    'type': 'BUY' if pos.type == 0 else 'SELL',
                    'volume': pos.volume,
                    'price_open': pos.price_open,
                    'price_current': pos.price_current,
                    'profit': pos.profit,
                    'swap': pos.swap,
                    'comment': pos.comment,
                    'magic': pos.magic,
                    'identifier': pos.identifier
                }
                position_list.append(position_dict)
            
            return position_list
            
        except Exception as e:
            logger.error(f"Erro ao obter posições abertas: {e}")
            return [] 