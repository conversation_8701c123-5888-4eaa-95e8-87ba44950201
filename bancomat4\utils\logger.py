#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Sistema de logging aprimorado para Bancomat 4
- Logs estruturados com rotação
- Cores no console
- Níveis personalizados
"""

import logging
import logging.handlers
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any
import json
import traceback


class ColoredFormatter(logging.Formatter):
    """Formatter com cores para console"""
    
    COLORS = {
        'DEBUG': '\033[36m',     # Cyan
        'INFO': '\033[32m',      # Green
        'WARNING': '\033[33m',   # Yellow
        'ERROR': '\033[31m',     # Red
        'CRITICAL': '\033[35m',  # Magenta
    }
    RESET = '\033[0m'
    
    def format(self, record):
        # Adiciona cor ao nível de log
        levelname = record.levelname
        if levelname in self.COLORS:
            record.levelname = f"{self.COLORS[levelname]}{levelname}{self.RESET}"
        
        # Formata a mensagem
        formatted = super().format(record)
        
        # Reseta a cor no final
        return f"{formatted}{self.RESET}"


class JsonFormatter(logging.Formatter):
    """Formatter que gera logs em JSON"""
    
    def format(self, record):
        log_data = {
            'timestamp': datetime.utcnow().isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno
        }
        
        # Adiciona exceção se houver
        if record.exc_info:
            log_data['exception'] = self.formatException(record.exc_info)
        
        # Adiciona dados extras se houver
        if hasattr(record, 'extra_data'):
            log_data['extra'] = record.extra_data
        
        return json.dumps(log_data, ensure_ascii=False)


class BancomatLogger:
    """Sistema de logging centralizado do Bancomat 4"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        self.loggers = {}
        self._setup_logging()
    
    def _setup_logging(self):
        """Configura o sistema de logging"""
        # Configurações padrão
        log_level = self.config.get('level', 'INFO')
        log_format = self.config.get('format', '%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        max_file_size = self.config.get('max_file_size', 10 * 1024 * 1024)  # 10MB
        max_files = self.config.get('max_files', 5)
        
        # Cria diretório de logs
        log_dir = Path('logs')
        log_dir.mkdir(exist_ok=True)
        
        # Configura o logger raiz
        root_logger = logging.getLogger()
        root_logger.setLevel(getattr(logging, log_level))
        
        # Remove handlers existentes
        root_logger.handlers = []
        
        # Handler para console com cores
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(getattr(logging, log_level))
        console_formatter = ColoredFormatter(log_format)
        console_handler.setFormatter(console_formatter)
        root_logger.addHandler(console_handler)
        
        # Handler para arquivo com rotação
        file_handler = logging.handlers.RotatingFileHandler(
            log_dir / 'bancomat4.log',
            maxBytes=max_file_size,
            backupCount=max_files,
            encoding='utf-8'
        )
        file_handler.setLevel(getattr(logging, log_level))
        file_formatter = logging.Formatter(log_format)
        file_handler.setFormatter(file_formatter)
        root_logger.addHandler(file_handler)
        
        # Handler para arquivo JSON (para análise posterior)
        json_handler = logging.handlers.RotatingFileHandler(
            log_dir / 'bancomat4.json.log',
            maxBytes=max_file_size,
            backupCount=max_files,
            encoding='utf-8'
        )
        json_handler.setLevel(logging.INFO)  # Apenas INFO e acima no JSON
        json_formatter = JsonFormatter()
        json_handler.setFormatter(json_formatter)
        root_logger.addHandler(json_handler)
        
        # Handler para erros críticos
        error_handler = logging.handlers.RotatingFileHandler(
            log_dir / 'bancomat4_errors.log',
            maxBytes=max_file_size,
            backupCount=max_files,
            encoding='utf-8'
        )
        error_handler.setLevel(logging.ERROR)
        error_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s\n'
            'Module: %(module)s - Function: %(funcName)s - Line: %(lineno)d\n'
            '%(pathname)s\n'
        )
        error_handler.setFormatter(error_formatter)
        root_logger.addHandler(error_handler)
    
    def get_logger(self, name: str) -> logging.Logger:
        """
        Obtém um logger específico
        
        Args:
            name: Nome do logger
            
        Returns:
            Logger configurado
        """
        if name not in self.loggers:
            self.loggers[name] = logging.getLogger(name)
        return self.loggers[name]
    
    def log_trade(self, action: str, trade_data: Dict[str, Any], logger: Optional[logging.Logger] = None):
        """
        Log especializado para trades
        
        Args:
            action: Ação realizada (open, close, update, etc)
            trade_data: Dados do trade
            logger: Logger a usar (opcional)
        """
        if logger is None:
            logger = self.get_logger('trading')
        
        logger.info(f"TRADE {action.upper()}: {trade_data.get('pair_name', 'Unknown')}", 
                   extra={'extra_data': {'action': action, 'trade': trade_data}})
    
    def log_signal(self, signal_data: Dict[str, Any], logger: Optional[logging.Logger] = None):
        """
        Log especializado para sinais
        
        Args:
            signal_data: Dados do sinal
            logger: Logger a usar (opcional)
        """
        if logger is None:
            logger = self.get_logger('signals')
        
        logger.info(f"SIGNAL: {signal_data.get('pair_name', 'Unknown')} - Score: {signal_data.get('score', 0):.2f}", 
                   extra={'extra_data': {'signal': signal_data}})
    
    def log_error(self, error: Exception, context: str = "", logger: Optional[logging.Logger] = None):
        """
        Log especializado para erros
        
        Args:
            error: Exceção ocorrida
            context: Contexto do erro
            logger: Logger a usar (opcional)
        """
        if logger is None:
            logger = self.get_logger('errors')
        
        error_data = {
            'error_type': type(error).__name__,
            'error_message': str(error),
            'context': context,
            'traceback': traceback.format_exc()
        }
        
        logger.error(f"ERROR in {context}: {type(error).__name__} - {str(error)}", 
                    extra={'extra_data': error_data})
    
    def log_performance(self, metric: str, value: float, unit: str = "", logger: Optional[logging.Logger] = None):
        """
        Log especializado para métricas de performance
        
        Args:
            metric: Nome da métrica
            value: Valor da métrica
            unit: Unidade de medida
            logger: Logger a usar (opcional)
        """
        if logger is None:
            logger = self.get_logger('performance')
        
        perf_data = {
            'metric': metric,
            'value': value,
            'unit': unit,
            'timestamp': datetime.utcnow().isoformat()
        }
        
        logger.info(f"PERFORMANCE: {metric} = {value:.2f} {unit}", 
                   extra={'extra_data': perf_data})


# Instância global do logger
_logger_instance = None


def setup_logging(config: Optional[Dict[str, Any]] = None) -> BancomatLogger:
    """
    Configura e retorna o sistema de logging
    
    Args:
        config: Configuração de logging
        
    Returns:
        Instância do BancomatLogger
    """
    global _logger_instance
    if _logger_instance is None:
        _logger_instance = BancomatLogger(config)
    return _logger_instance


def get_logger(name: str) -> logging.Logger:
    """
    Obtém um logger específico
    
    Args:
        name: Nome do logger
        
    Returns:
        Logger configurado
    """
    if _logger_instance is None:
        setup_logging()
    return _logger_instance.get_logger(name) 