#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Bancomat 4 - Expert Advisor para Trading de Pares Forex
"""

__version__ = "4.0.0"
__author__ = "Bancomat Team"
__description__ = "Expert Advisor avançado para trading contínuo de pares forex"

from utils.logger import setup_logging, get_logger
from utils.config import get_config_manager

# Configuração inicial
logger = get_logger(__name__)
logger.info(f"Bancomat 4 v{__version__} - {__description__}")

# Exportações principais
__all__ = [
    '__version__',
    '__author__', 
    '__description__',
    'setup_logging',
    'get_logger',
    'get_config_manager'
] 