#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Melhorias para a Aba de Trading do Bancomat 4
- Configurações funcionais com salvamento
- Carregamento de valores atuais da configuração
- Validação e feedback visual
"""

import dearpygui.dearpygui as dpg
from datetime import datetime
from utils.logger import get_logger

logger = get_logger(__name__)


def enhance_trading_tab(interface_instance):
    """
    Aplica melhorias na aba de trading da interface
    
    Args:
        interface_instance: Instância da AdvancedInterface
    """
    try:
        # Adiciona métodos de configuração à instância
        interface_instance.save_trading_config = lambda: save_trading_config(interface_instance)
        interface_instance.reset_trading_config = lambda: reset_trading_config(interface_instance)
        interface_instance.reload_trading_config = lambda: reload_trading_config(interface_instance)
        interface_instance.update_trading_fields = lambda: update_trading_fields(interface_instance)
        
        logger.info("✅ Melhorias da aba de trading aplicadas")
        
    except Exception as e:
        logger.error(f"❌ Erro ao aplicar melhorias: {e}")


def save_trading_config(interface_instance):
    """Salva configurações de trading da interface"""
    try:
        logger.info("💾 Salvando configurações de trading...")
        
        # Obtém valores dos campos da interface
        max_trades = dpg.get_value("config_max_trades") if dpg.does_item_exist("config_max_trades") else 5
        risk_per_trade = dpg.get_value("config_risk_per_trade") if dpg.does_item_exist("config_risk_per_trade") else 1.0
        min_zscore = dpg.get_value("config_min_zscore") if dpg.does_item_exist("config_min_zscore") else 2.0
        max_volume = dpg.get_value("config_max_volume") if dpg.does_item_exist("config_max_volume") else 0.05
        min_score = dpg.get_value("config_min_score") if dpg.does_item_exist("config_min_score") else 60
        
        # Valida valores
        if max_trades < 1 or max_trades > 20:
            raise ValueError("Max Trades deve estar entre 1 e 20")
        if risk_per_trade < 0.1 or risk_per_trade > 10.0:
            raise ValueError("Risco por Trade deve estar entre 0.1% e 10%")
        if min_zscore < 0.5 or min_zscore > 5.0:
            raise ValueError("Z-Score Mínimo deve estar entre 0.5 e 5.0")
        if max_volume < 0.01 or max_volume > 1.0:
            raise ValueError("Volume Máximo deve estar entre 0.01 e 1.0")
        if min_score < 0 or min_score > 100:
            raise ValueError("Score Mínimo deve estar entre 0 e 100")
        
        # Salva na configuração
        interface_instance.config.set('trading.max_simultaneous_trades', max_trades)
        interface_instance.config.set('risk.risk_per_trade', risk_per_trade)
        interface_instance.config.set('signals.min_zscore', min_zscore)
        interface_instance.config.set('risk.max_volume_per_trade', max_volume)
        interface_instance.config.set('signals.min_score', min_score)
        
        # Salva arquivo de configuração
        interface_instance.config.save_user_config()
        
        # Feedback na interface
        log_to_interface(interface_instance, "✅ Configurações de trading salvas com sucesso!")
        
        # Atualiza AutoTrader se disponível
        if hasattr(interface_instance, 'auto_trader') and interface_instance.auto_trader:
            # Recarrega configurações no trader
            interface_instance.auto_trader.trading_config = interface_instance.config.get_section('trading')
            interface_instance.auto_trader.risk_config = interface_instance.config.get_section('risk')
            interface_instance.auto_trader.signals_config = interface_instance.config.get_section('signals')
            
            log_to_interface(interface_instance, "🔄 Configurações aplicadas ao AutoTrader")
        
        logger.info("✅ Configurações de trading salvas com sucesso")
        
        # Salva também no arquivo YAML
        _save_config_summary(interface_instance)
        
    except ValueError as e:
        error_msg = f"❌ Erro de validação: {str(e)}"
        logger.error(error_msg)
        log_to_interface(interface_instance, error_msg)
        
    except Exception as e:
        error_msg = f"❌ Erro ao salvar configurações: {str(e)}"
        logger.error(error_msg)
        log_to_interface(interface_instance, error_msg)


def reset_trading_config(interface_instance):
    """Restaura configurações padrão de trading"""
    try:
        logger.info("🔄 Restaurando configurações padrão...")
        
        # Valores padrão
        defaults = {
            'max_trades': 5,
            'risk_per_trade': 1.0,
            'min_zscore': 2.0,
            'max_volume': 0.05,
            'min_score': 60
        }
        
        # Atualiza campos da interface
        if dpg.does_item_exist("config_max_trades"):
            dpg.set_value("config_max_trades", defaults['max_trades'])
        if dpg.does_item_exist("config_risk_per_trade"):
            dpg.set_value("config_risk_per_trade", defaults['risk_per_trade'])
        if dpg.does_item_exist("config_min_zscore"):
            dpg.set_value("config_min_zscore", defaults['min_zscore'])
        if dpg.does_item_exist("config_max_volume"):
            dpg.set_value("config_max_volume", defaults['max_volume'])
        if dpg.does_item_exist("config_min_score"):
            dpg.set_value("config_min_score", defaults['min_score'])
        
        log_to_interface(interface_instance, "🔄 Configurações restauradas aos padrões (clique em Salvar para aplicar)")
        logger.info("✅ Configurações restauradas aos padrões")
        
    except Exception as e:
        error_msg = f"❌ Erro ao restaurar configurações: {str(e)}"
        logger.error(error_msg)
        log_to_interface(interface_instance, error_msg)


def reload_trading_config(interface_instance):
    """Recarrega configurações do arquivo"""
    try:
        logger.info("📂 Recarregando configurações do arquivo...")
        
        # Recarrega configuração
        interface_instance.config.reload()
        
        # Atualiza campos da interface
        update_trading_fields(interface_instance)
        
        log_to_interface(interface_instance, "📂 Configurações recarregadas do arquivo")
        logger.info("✅ Configurações recarregadas")
        
    except Exception as e:
        error_msg = f"❌ Erro ao recarregar configurações: {str(e)}"
        logger.error(error_msg)
        log_to_interface(interface_instance, error_msg)


def update_trading_fields(interface_instance):
    """Atualiza campos da interface com valores atuais da configuração"""
    try:
        # Obtém valores atuais
        current_max_trades = interface_instance.config.get('trading.max_simultaneous_trades', 5)
        current_risk = interface_instance.config.get('risk.risk_per_trade', 1.0)
        current_min_zscore = interface_instance.config.get('signals.min_zscore', 2.0)
        current_max_volume = interface_instance.config.get('risk.max_volume_per_trade', 0.05)
        current_min_score = interface_instance.config.get('signals.min_score', 60)
        
        # Atualiza campos se existirem
        if dpg.does_item_exist("config_max_trades"):
            dpg.set_value("config_max_trades", current_max_trades)
        if dpg.does_item_exist("config_risk_per_trade"):
            dpg.set_value("config_risk_per_trade", current_risk)
        if dpg.does_item_exist("config_min_zscore"):
            dpg.set_value("config_min_zscore", current_min_zscore)
        if dpg.does_item_exist("config_max_volume"):
            dpg.set_value("config_max_volume", current_max_volume)
        if dpg.does_item_exist("config_min_score"):
            dpg.set_value("config_min_score", current_min_score)
        
        logger.debug("🔄 Campos de configuração atualizados")
        
    except Exception as e:
        logger.error(f"Erro ao atualizar campos: {e}")


def log_to_interface(interface_instance, message: str):
    """Adiciona mensagem aos logs da interface"""
    try:
        if hasattr(interface_instance, '_log_to_interface'):
            interface_instance._log_to_interface(message)
        elif 'logs_text' in interface_instance.tags:
            timestamp = datetime.now().strftime("%H:%M:%S")
            log_entry = f"{timestamp} - {message}\n"
            current_logs = dpg.get_value(interface_instance.tags['logs_text'])
            dpg.set_value(interface_instance.tags['logs_text'], current_logs + log_entry)
    except Exception:
        pass  # Falha silenciosa se logs não disponíveis


def _save_config_summary(interface_instance):
    """Salva resumo das configurações em arquivo separado"""
    try:
        import json
        from pathlib import Path
        
        config_summary = {
            'timestamp': datetime.now().isoformat(),
            'trading': {
                'max_simultaneous_trades': interface_instance.config.get('trading.max_simultaneous_trades', 5),
                'risk_per_trade': interface_instance.config.get('risk.risk_per_trade', 1.0),
                'min_zscore': interface_instance.config.get('signals.min_zscore', 2.0),
                'max_volume_per_trade': interface_instance.config.get('risk.max_volume_per_trade', 0.05),
                'min_score': interface_instance.config.get('signals.min_score', 60)
            }
        }
        
        config_file = Path("data") / "trading_config_backup.json"
        config_file.parent.mkdir(exist_ok=True)
        
        with open(config_file, 'w') as f:
            json.dump(config_summary, f, indent=2)
        
        logger.info(f"📁 Backup de configuração salvo em {config_file}")
        
    except Exception as e:
        logger.error(f"Erro ao salvar backup: {e}")


# Função auxiliar para criar campos melhorados
def create_enhanced_trading_fields():
    """
    Retorna código para substituir os campos básicos por versões melhoradas
    
    Returns:
        str: Código Python com os campos melhorados
    """
    return '''
    # Configurações básicas
    dpg.add_text("CONFIGURACOES BASICAS")
    dpg.add_separator()
    
    # Obtém valores atuais da configuração
    current_max_trades = self.config.get('trading.max_simultaneous_trades', 5)
    current_risk = self.config.get('risk.risk_per_trade', 1.0)
    current_min_zscore = self.config.get('signals.min_zscore', 2.0)
    current_max_volume = self.config.get('risk.max_volume_per_trade', 0.05)
    current_min_score = self.config.get('signals.min_score', 60)
    
    # Campos editáveis com valores atuais
    dpg.add_input_int(label="Max Trades Simultaneos", 
                     default_value=current_max_trades, 
                     tag="config_max_trades", 
                     width=200,
                     min_value=1, max_value=20,
                     min_clamped=True, max_clamped=True)
    
    dpg.add_input_float(label="Risco por Trade (%)", 
                       default_value=current_risk, 
                       tag="config_risk_per_trade", 
                       width=200,
                       min_value=0.1, max_value=10.0,
                       min_clamped=True, max_clamped=True,
                       format="%.1f")
    
    dpg.add_input_float(label="Z-Score Minimo", 
                       default_value=current_min_zscore, 
                       tag="config_min_zscore", 
                       width=200,
                       min_value=0.5, max_value=5.0,
                       min_clamped=True, max_clamped=True,
                       format="%.1f")
    
    dpg.add_input_float(label="Volume Max por Trade", 
                       default_value=current_max_volume, 
                       tag="config_max_volume", 
                       width=200,
                       min_value=0.01, max_value=1.0,
                       min_clamped=True, max_clamped=True,
                       format="%.2f")
    
    dpg.add_input_int(label="Score Minimo", 
                     default_value=current_min_score, 
                     tag="config_min_score", 
                     width=200,
                     min_value=0, max_value=100,
                     min_clamped=True, max_clamped=True)
    
    dpg.add_spacer(height=20)
    
    # Botões de configuração
    dpg.add_text("GERENCIAR CONFIGURACOES")
    dpg.add_separator()
    
    dpg.add_button(label="💾 Salvar Configurações", 
                  callback=self.save_trading_config, 
                  width=300)
    
    dpg.add_button(label="🔄 Restaurar Padrões", 
                  callback=self.reset_trading_config, 
                  width=300)
    
    dpg.add_button(label="📂 Recarregar", 
                  callback=self.reload_trading_config, 
                  width=300)
    '''


if __name__ == "__main__":
    print("🔧 Trading Config Enhancement para Bancomat 4")
    print("Este módulo melhora a funcionalidade de configuração da aba de trading")
    print("\nFuncionalidades:")
    print("✅ Carregamento de valores atuais da configuração")
    print("✅ Salvamento funcional com validação")
    print("✅ Backup automático das configurações")
    print("✅ Restauração de padrões")
    print("✅ Recarregamento do arquivo")
    print("✅ Feedback visual na interface") 