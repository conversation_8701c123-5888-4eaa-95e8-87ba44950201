#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Correção dos Erros de Trading na Interface - Bancomat 4
- Corrige 'AdvancedInterface' object has no attribute 'active_trades'
- Garante inicialização correta do AutoTrader
- Corrige referências incorretas na interface
"""

import sys
from pathlib import Path

# Adiciona o diretório do projeto ao path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from utils.logger import get_logger

# Configurar logger
logger = get_logger(__name__)

def fix_active_trades_reference():
    """Corrige referências incorretas ao active_trades na interface"""
    
    print("🔧 CORRIGINDO REFERÊNCIAS AO ACTIVE_TRADES")
    print("=" * 60)
    
    interface_file = project_root / "ui" / "advanced_interface.py"
    
    # Lê o arquivo atual
    with open(interface_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Correções necessárias
    corrections = [
        # Corrige referência direta ao active_trades
        (
            'dpg.set_value(self.tags["active_trades"], f"Trades Ativos: {len(self.active_trades)}")',
            'dpg.set_value(self.tags["active_trades"], f"Trades Ativos: 0")'
        ),
        
        # Adiciona inicialização do active_trades no construtor se necessário
        ('self.stats = {', '''self.stats = {
            'total_trades': 0,
            'profitable_trades': 0, 
            'total_profit': 0.0
        }
        
        # Fallback para active_trades como lista vazia
        self.fallback_active_trades = []
        
        self.stats = {'''),
    ]
    
    # Aplica correções
    modified = False
    for old_text, new_text in corrections:
        if old_text in content:
            content = content.replace(old_text, new_text)
            modified = True
            print(f"✅ Correção aplicada: {old_text[:50]}...")
    
    # Adiciona método de inicialização do AutoTrader
    auto_trader_init_method = '''
    def initialize_auto_trader(self):
        """Inicializa o AutoTrader se disponível"""
        try:
            if not hasattr(self, 'auto_trader') or self.auto_trader is None:
                # Tenta importar e inicializar AutoTrader
                try:
                    from core.trader import AutoTrader
                    from core.analyzer import PairAnalyzer
                    
                    # Inicializa analyzer se não existe
                    if not hasattr(self, 'analyzer') or self.analyzer is None:
                        self.analyzer = PairAnalyzer(self.config, self.mt5)
                    
                    # Inicializa AutoTrader
                    self.auto_trader = AutoTrader(self.config, self.mt5, self.analyzer)
                    logger.info("✅ AutoTrader inicializado com sucesso")
                    
                    if 'trading_status' in self.tags:
                        dpg.set_value(self.tags['trading_status'], "Status: Sistema Pronto")
                        
                except ImportError as e:
                    logger.warning(f"⚠️ Não foi possível importar AutoTrader: {e}")
                    self.auto_trader = None
                except Exception as e:
                    logger.error(f"❌ Erro ao inicializar AutoTrader: {e}")
                    self.auto_trader = None
        except Exception as e:
            logger.error(f"❌ Erro na inicialização do AutoTrader: {e}")
            self.auto_trader = None
'''
    
    # Insere o método se não existir
    if 'initialize_auto_trader' not in content:
        # Encontra local para inserir (antes do último método)
        insertion_point = content.rfind('\n    def ')
        if insertion_point != -1:
            content = (content[:insertion_point] + 
                      auto_trader_init_method + 
                      content[insertion_point:])
            modified = True
            print("✅ Método initialize_auto_trader adicionado")
    
    # Adiciona chamada da inicialização no create_interface
    if 'self.initialize_auto_trader()' not in content:
        # Procura pelo final do método create_interface
        create_interface_pos = content.find('def create_interface(self):')
        if create_interface_pos != -1:
            # Encontra o final do método
            next_def_pos = content.find('\n    def ', create_interface_pos + 1)
            if next_def_pos != -1:
                # Insere antes do próximo método
                insertion_text = '''
        # Inicializa AutoTrader
        self.initialize_auto_trader()
        
'''
                content = (content[:next_def_pos] + 
                          insertion_text + 
                          content[next_def_pos:])
                modified = True
                print("✅ Chamada para initialize_auto_trader adicionada")
    
    # Salva arquivo se modificado
    if modified:
        with open(interface_file, 'w', encoding='utf-8') as f:
            f.write(content)
        print("✅ Arquivo interface atualizado")
    else:
        print("✅ Nenhuma modificação necessária")

def fix_start_auto_trading_method():
    """Corrige o método start_auto_trading para melhor robustez"""
    
    print("\n🔧 MELHORANDO MÉTODO START_AUTO_TRADING")
    print("=" * 60)
    
    interface_file = project_root / "ui" / "advanced_interface.py"
    
    # Lê o arquivo atual
    with open(interface_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Método melhorado
    improved_start_method = '''    def start_auto_trading(self):
        """Inicia trading automático com verificações robustas"""
        try:
            logger.info("🚀 Tentando iniciar trading automático...")
            
            # Verifica se AutoTrader está disponível
            if not hasattr(self, 'auto_trader') or self.auto_trader is None:
                logger.warning("⚠️ AutoTrader não disponível, tentando inicializar...")
                self.initialize_auto_trader()
                
                if self.auto_trader is None:
                    logger.error("❌ Não foi possível inicializar AutoTrader")
                    if 'trading_status' in self.tags:
                        dpg.set_value(self.tags['trading_status'], 
                                     "Status: Erro - AutoTrader não disponível")
                    return
            
            # Verifica conexão MT5
            if not self.mt5 or not self.mt5.is_connected():
                logger.error("❌ MT5 não conectado")
                if 'trading_status' in self.tags:
                    dpg.set_value(self.tags['trading_status'], 
                                 "Status: Erro - MT5 desconectado")
                return
            
            # Verifica se já está rodando
            if hasattr(self.auto_trader, 'running') and self.auto_trader.running:
                logger.warning("⚠️ AutoTrader já está rodando")
                if 'trading_status' in self.tags:
                    dpg.set_value(self.tags['trading_status'], 
                                 "Status: Trading Automático")
                return
            
            # Inicia o trading
            if hasattr(self.auto_trader, 'start_trading'):
                self.auto_trader.start_trading()
                logger.info("✅ Trading automático iniciado")
                
                # Atualiza interface
                if 'trading_status' in self.tags:
                    dpg.set_value(self.tags['trading_status'], 
                                 "Status: Trading Automático")
                if 'trading_mode' in self.tags:
                    dpg.set_value(self.tags['trading_mode'], 
                                 "Modo: Automático")
            else:
                logger.error("❌ Método start_trading não encontrado no AutoTrader")
                
        except Exception as e:
            logger.error(f"❌ Erro ao iniciar trading automático: {e}")
            if 'trading_status' in self.tags:
                dpg.set_value(self.tags['trading_status'], 
                             f"Status: Erro - {str(e)[:30]}")'''
    
    # Procura e substitui o método existente
    start_pos = content.find('def start_auto_trading(self):')
    if start_pos != -1:
        # Encontra o final do método
        next_def_pos = content.find('\n    def ', start_pos + 1)
        if next_def_pos == -1:
            next_def_pos = len(content)
        
        # Substitui o método
        new_content = (content[:start_pos] + 
                      improved_start_method[4:] +  # Remove indentação extra
                      content[next_def_pos:])
        
        # Salva arquivo
        with open(interface_file, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print("✅ Método start_auto_trading melhorado")
    else:
        print("❌ Método start_auto_trading não encontrado")

def add_stats_initialization():
    """Garante que stats seja inicializado corretamente"""
    
    print("\n🔧 GARANTINDO INICIALIZAÇÃO DO STATS")
    print("=" * 60)
    
    interface_file = project_root / "ui" / "advanced_interface.py"
    
    # Lê o arquivo atual
    with open(interface_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Procura pelo construtor
    init_pos = content.find('def __init__(self, config: ConfigManager, mt5: MT5Connector):')
    if init_pos != -1:
        # Procura onde adicionar a inicialização
        next_def_pos = content.find('\n    def ', init_pos + 1)
        if next_def_pos != -1:
            # Verifica se stats já está inicializado
            init_section = content[init_pos:next_def_pos]
            if 'self.stats = {' not in init_section:
                # Adiciona inicialização do stats
                stats_init = '''
        # Inicializa estatísticas
        self.stats = {
            'total_trades': 0,
            'profitable_trades': 0,
            'total_profit': 0.0
        }
        
'''
                # Insere antes do final do construtor
                content = (content[:next_def_pos - 1] + 
                          stats_init + 
                          content[next_def_pos - 1:])
                
                # Salva arquivo
                with open(interface_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                print("✅ Inicialização do stats adicionada")
            else:
                print("✅ Stats já está inicializado")
    else:
        print("❌ Construtor não encontrado")

def create_interface_health_check():
    """Cria script de verificação de saúde da interface"""
    
    print("\n🔧 CRIANDO VERIFICAÇÃO DE SAÚDE DA INTERFACE")
    print("=" * 60)
    
    health_check_code = '''#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Verificação de Saúde da Interface - Bancomat 4
- Verifica se todos os componentes estão funcionando
- Identifica problemas na inicialização
- Gera relatório de status
"""

import sys
from pathlib import Path

# Adiciona o diretório do projeto ao path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_interface_health():
    """Verifica a saúde da interface"""
    
    print("🏥 VERIFICAÇÃO DE SAÚDE DA INTERFACE")
    print("=" * 50)
    
    try:
        # Testa importações
        print("📦 Testando importações...")
        
        from utils.config import ConfigManager
        print("✅ ConfigManager importado")
        
        from core.mt5_connector import MT5Connector  
        print("✅ MT5Connector importado")
        
        from ui.advanced_interface import AdvancedInterface
        print("✅ AdvancedInterface importado")
        
        # Testa inicialização básica
        print("\n🔧 Testando inicialização...")
        
        config = ConfigManager()
        print("✅ ConfigManager inicializado")
        
        mt5 = MT5Connector(config)
        print("✅ MT5Connector inicializado")
        
        # Testa criação da interface (sem executar)
        interface = AdvancedInterface(config, mt5)
        print("✅ AdvancedInterface criado")
        
        # Verifica atributos essenciais
        print("\n📋 Verificando atributos...")
        
        if hasattr(interface, 'config'):
            print("✅ Atributo config presente")
        else:
            print("❌ Atributo config ausente")
            
        if hasattr(interface, 'mt5'):
            print("✅ Atributo mt5 presente")
        else:
            print("❌ Atributo mt5 ausente")
            
        if hasattr(interface, 'stats'):
            print("✅ Atributo stats presente")
        else:
            print("❌ Atributo stats ausente")
            
        # Verifica métodos críticos
        print("\n🔍 Verificando métodos...")
        
        critical_methods = [
            'create_interface',
            'update_trading_data', 
            'start_auto_trading',
            'initialize_auto_trader'
        ]
        
        for method in critical_methods:
            if hasattr(interface, method):
                print(f"✅ Método {method} presente")
            else:
                print(f"❌ Método {method} ausente")
        
        print("\n✅ VERIFICAÇÃO CONCLUÍDA")
        print("Interface parece estar funcionando corretamente")
        
    except ImportError as e:
        print(f"❌ Erro de importação: {e}")
    except Exception as e:
        print(f"❌ Erro durante verificação: {e}")

if __name__ == "__main__":
    check_interface_health()
'''
    
    health_check_file = project_root / "interface_health_check.py"
    with open(health_check_file, 'w', encoding='utf-8') as f:
        f.write(health_check_code)
    
    print(f"✅ Verificador de saúde criado: {health_check_file}")

def main():
    """Executa todas as correções"""
    
    print("🔧 CORREÇÃO DOS ERROS DE TRADING NA INTERFACE")
    print("🎯 Corrigindo active_trades e inicialização do AutoTrader")
    print("=" * 70)
    
    # Corrige referências ao active_trades
    fix_active_trades_reference()
    
    # Melhora método start_auto_trading
    fix_start_auto_trading_method()
    
    # Garante inicialização do stats
    add_stats_initialization()
    
    # Cria verificador de saúde
    create_interface_health_check()
    
    print(f"\n✅ CORREÇÕES APLICADAS COM SUCESSO!")
    print(f"📋 PRÓXIMOS PASSOS:")
    print(f"   1. Testar: python interface_health_check.py")
    print(f"   2. Reiniciar sistema para aplicar correções")
    print(f"   3. Verificar logs para confirmar AutoTrader inicializado")
    print(f"   4. Monitorar erros na interface")

if __name__ == "__main__":
    main() 