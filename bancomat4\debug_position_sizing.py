#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Debug do Position Sizing - Investigação Detalhada
- Mostra exatamente como os volumes estão sendo calculados
- Identifica por que há salto de 0.01 para 0.05 por diferenças pequenas
"""

import json
import sys
from pathlib import Path

# Adiciona o diretório do projeto ao path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from utils.config import ConfigManager

def debug_volume_calculation(trade_data, risk_config):
    """Simula exatamente o cálculo do position sizing"""
    
    print(f"🔍 DEBUG: {trade_data['pair_name']}")
    print("=" * 50)
    
    # Parâmetros base
    base_volume = risk_config.get('base_volume', 0.01)
    max_volume = risk_config.get('max_volume_per_trade', 0.05)
    max_multiplier = risk_config.get('max_volume_multiplier', 5)
    risk_per_trade = risk_config.get('risk_per_trade', 1) / 100
    
    print(f"📊 PARÂMETROS BASE:")
    print(f"   • Base Volume: {base_volume}")
    print(f"   • Max Volume: {max_volume}")
    print(f"   • Max Multiplier: {max_multiplier}")
    print(f"   • Risk per Trade: {risk_per_trade*100}%")
    
    # Dados do trade
    zscore = abs(trade_data['entry_zscore'])
    score = trade_data['signal_score']
    beta = abs(trade_data['beta'])
    
    print(f"\n📈 DADOS DO SINAL:")
    print(f"   • Z-Score: {zscore:.3f}")
    print(f"   • Score: {score:.1f}")
    print(f"   • Beta: {beta:.4f}")
    
    # PASSO 1: Confidence Multiplier
    confidence = score / 100
    confidence_multiplier = 0.5 + (confidence * 0.5)
    
    print(f"\n🎯 PASSO 1 - Confiança:")
    print(f"   • Confidence: {confidence:.3f}")
    print(f"   • Confidence Multiplier: {confidence_multiplier:.3f}")
    
    # PASSO 2: Z-Score Multiplier
    if zscore >= 2.0:
        zscore_multiplier = min(1.0 + (zscore - 2.0) * 0.2, 2.0)
    else:
        zscore_multiplier = 1.0
    
    print(f"\n📊 PASSO 2 - Z-Score:")
    print(f"   • Z-Score Multiplier: {zscore_multiplier:.3f}")
    
    # PASSO 3: Cálculo Base
    volume_step1 = base_volume
    print(f"\n🔢 PASSO 3 - Cálculo Base:")
    print(f"   • Volume inicial: {volume_step1:.4f}")
    
    volume_step2 = volume_step1 * zscore_multiplier
    print(f"   • Após Z-Score: {volume_step2:.4f}")
    
    volume_step3 = volume_step2 * confidence_multiplier
    print(f"   • Após Confiança: {volume_step3:.4f}")
    
    # PASSO 4: Aplicação do Max Volume Multiplier
    max_volume_from_base = base_volume * max_multiplier
    print(f"\n⚡ PASSO 4 - Limitação:")
    print(f"   • Max Volume from Base: {max_volume_from_base:.4f}")
    
    volume_step4 = min(volume_step3, max_volume_from_base)
    print(f"   • Após Max Multiplier: {volume_step4:.4f}")
    
    # PASSO 5: Limite absoluto
    volume_final = min(volume_step4, max_volume)
    print(f"   • Após Max Absolute: {volume_final:.4f}")
    
    # PASSO 6: Ajuste para lote mínimo
    if volume_final < base_volume:
        volume_final = base_volume
    
    print(f"   • Volume Final: {volume_final:.4f}")
    
    # Comparação com real
    volume_real = trade_data['symbol1_volume']
    print(f"\n✅ RESULTADO:")
    print(f"   • Volume Calculado: {volume_final:.4f}")
    print(f"   • Volume Real: {volume_real:.4f}")
    print(f"   • Diferença: {abs(volume_real - volume_final):.4f}")
    
    if abs(volume_real - volume_final) > 0.001:
        print(f"   ⚠️ DISCREPÂNCIA DETECTADA!")
        
        # Vamos testar se há outro algoritmo
        print(f"\n🔍 INVESTIGAÇÃO ADICIONAL:")
        
        # Talvez seja aplicação direta de thresholds
        if score >= 65:
            threshold_volume = max_volume
            print(f"   • Se Score >= 65: {threshold_volume}")
        elif score >= 62:
            threshold_volume = max_volume * 0.8
            print(f"   • Se Score >= 62: {threshold_volume}")
        else:
            threshold_volume = base_volume
            print(f"   • Se Score < 62: {threshold_volume}")
        
        if abs(volume_real - threshold_volume) < 0.001:
            print(f"   🎯 POSSÍVEL CAUSA: Sistema usando thresholds fixos!")
    
    print("\n" + "=" * 50)
    return volume_final

def main():
    print("🔍 DEBUG DETALHADO DO POSITION SIZING")
    print("🎯 Investigando escalação desproporcional dos volumes")
    print("=" * 70)
    
    # Carrega os trades ativos
    trades_file = project_root / "data" / "bancomat4_trades.json"
    if not trades_file.exists():
        print("❌ Arquivo de trades não encontrado")
        return
    
    with open(trades_file, 'r') as f:
        trades = json.load(f)
    
    # Carrega configuração
    config = ConfigManager()
    risk_config = config.get_section('risk')
    
    print(f"\n📊 ANALISANDO {len(trades)} TRADES:\n")
    
    # Debug cada trade
    for trade in trades:
        debug_volume_calculation(trade, risk_config)
    
    print("\n🧠 ANÁLISE CRÍTICA:")
    print("   • Score 60.9 → Volume 0.01")
    print("   • Score 63.3 → Volume 0.05 (5x maior!)")  
    print("   • Score 68.1 → Volume 0.05")
    print(f"   • Diferença mínima de 2.4 pontos causou salto de 500%!")
    print(f"   • Isso indica threshold fixo ou bug no algoritmo")
    
    # Análise de sensibilidade
    print(f"\n📈 TESTE DE SENSIBILIDADE:")
    test_scores = [58, 60, 61, 62, 63, 65, 68, 70]
    
    for test_score in test_scores:
        confidence = test_score / 100
        confidence_multiplier = 0.5 + (confidence * 0.5)
        zscore_multiplier = 1.03  # Médio dos trades
        
        volume = 0.01 * zscore_multiplier * confidence_multiplier
        volume = min(volume, 0.05)
        
        print(f"   Score {test_score:2d} → Volume {volume:.4f}")

if __name__ == "__main__":
    main() 