#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script para corrigir método apply_pairs_filter faltante na interface
Adiciona o método faltante que estava causando erro na aba de pares
"""

import os
from pathlib import Path

def fix_missing_method():
    """Adiciona o método apply_pairs_filter faltante"""
    
    interface_file = Path("ui/advanced_interface.py")
    
    if not interface_file.exists():
        print("❌ Arquivo da interface não encontrado!")
        return False
    
    # Lê o arquivo
    with open(interface_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Verifica se o método já existe
    if "def apply_pairs_filter(self):" in content:
        print("✅ Método apply_pairs_filter já existe!")
        return True
    
    # Método a ser adicionado
    method_code = '''
    def apply_pairs_filter(self):
        """Aplica filtros na tabela de pares e atualiza exibição"""
        try:
            logger.debug("🔍 Aplicando filtros nos pares...")
            
            # Atualiza a tabela com os filtros aplicados
            self.update_pairs_table()
            
            # Atualiza estatísticas baseadas nos filtros
            if not hasattr(self, 'pairs_analysis_data'):
                return
            
            # Obtém filtros atuais com verificação de existência
            show_approved_only = False
            show_signals_only = False
            min_score = 0.0
            
            try:
                if dpg.does_item_exist("filter_approved_only"):
                    show_approved_only = dpg.get_value("filter_approved_only")
            except:
                pass
                
            try:
                if dpg.does_item_exist("filter_with_signals"):
                    show_signals_only = dpg.get_value("filter_with_signals")
            except:
                pass
                
            try:
                if dpg.does_item_exist("filter_min_score"):
                    min_score = dpg.get_value("filter_min_score")
            except:
                pass
            
            # Conta pares filtrados
            filtered_count = 0
            for pair_data in self.pairs_analysis_data:
                # Aplica os mesmos filtros da tabela
                if show_approved_only and pair_data['status'] not in ['APPROVED', 'BUY_SIGNAL', 'SELL_SIGNAL']:
                    continue
                
                if show_signals_only and pair_data['status'] not in ['BUY_SIGNAL', 'SELL_SIGNAL']:
                    continue
                
                if pair_data['score'] < min_score:
                    continue
                
                filtered_count += 1
            
            # Atualiza status de filtros
            if 'pairs_analysis_status' in self.tags:
                status_text = f"📊 {filtered_count} pares"
                if show_approved_only:
                    status_text += " (apenas aprovados)"
                if show_signals_only:
                    status_text += " (apenas com sinais)"
                if min_score > 0:
                    status_text += f" (score >= {min_score:.1f})"
                
                dpg.set_value(self.tags['pairs_analysis_status'], status_text)
            
            logger.debug(f"✅ Filtros aplicados - {filtered_count} pares exibidos")
            
        except Exception as e:
            logger.error(f"Erro ao aplicar filtros de pares: {e}")
'''
    
    # Encontra onde inserir o método (antes do último método)
    insertion_point = content.rfind("    def save_settings(self):")
    
    if insertion_point == -1:
        # Se não encontrar save_settings, insere antes do último método
        insertion_point = content.rfind("    def ")
        if insertion_point == -1:
            print("❌ Não foi possível encontrar ponto de inserção!")
            return False
    
    # Insere o método
    new_content = content[:insertion_point] + method_code + "\n" + content[insertion_point:]
    
    # Cria backup
    backup_file = interface_file.with_suffix('.py.backup_missing_method')
    with open(backup_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    # Salva arquivo corrigido
    with open(interface_file, 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print("✅ Método apply_pairs_filter adicionado com sucesso!")
    print(f"📁 Backup salvo em: {backup_file}")
    
    return True

if __name__ == "__main__":
    print("🔧 Corrigindo método faltante na interface...")
    
    if fix_missing_method():
        print("✅ Correção concluída!")
        print("🔄 Reinicie o Bancomat 4 para aplicar as correções")
    else:
        print("❌ Falha na correção") 