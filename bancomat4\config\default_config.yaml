# Configuração do Bancomat 4
# Apenas configurações realmente utilizadas pelos módulos implementados

mt5:
  terminal_path: "C:\\Program Files\\MetaTrader 5 - Copia\\terminal64.exe"
  login: 0
  password: ""
  server: ""
  timeout: 60000

trading:
  max_simultaneous_trades: 5
  magic_number: 12345678
  comment_prefix: "B4"
  timeout_multiplier: 3.0  # Multiplicador do half-life para calcular timeout
  auto_start: false  # Inicia trading automaticamente (false = só análise)
  
analysis:
  timeframes: [15, 30, 60]  # minutos
  update_interval: 10  # segundos
  cache_duration: 300  # segundos
  
risk:
  # Gestão geral de risco
  max_trades_global: 5
  max_volume_per_trade: 0.05
  max_drawdown_percent: 10.0
  
  # NOVO: Controle de exposição por moeda
  max_trades_per_currency: 2        # Máximo de trades usando a mesma moeda
  max_volume_per_currency: 0.08     # Volume máximo total por moeda
  currency_warning_threshold: 0.05  # Aviso quando volume > threshold
  
  # Controle de beta para compatibilidade de pares
  max_beta_threshold: 10.0
  min_beta_threshold: 0.1
  
  # Margem e posição
  min_free_margin: 1000
  margin_level_warning: 150
  margin_level_critical: 100
  
  max_exposure_percent: 10
  risk_per_trade: 1
  position_sizing: "proportional"
  base_volume: 0.01
  max_volume_multiplier: 5
  margin_safety_factor: 0.3  # Usa 30% da margem livre
  max_zscore: 4.0
  max_correlation: 0.95
  min_correlation: 0.60
  min_cointegration_pvalue: 0.10
  max_half_life: 96
  min_half_life: 5
  signal_expiry: 300  # segundos

# Configuração Signals
signals:
  min_score: 60
  min_zscore: 2.0
  max_zscore: 4.0
  min_correlation: 0.60
  max_correlation: 0.95
  min_cointegration_pvalue: 0.10
  min_half_life: 5
  max_half_life: 96
  signal_expiry: 300  # segundos 