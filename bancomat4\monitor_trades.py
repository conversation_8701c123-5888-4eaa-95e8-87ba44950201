#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Monitor de Status dos Trades - Bancomat 4
- Monitora atualização de Z-Scores
- Verifica controle de max trades
- Gera alertas para problemas
"""

import json
import time
from pathlib import Path
from datetime import datetime, timedelta

class TradeStatusMonitor:
    """Monitor de status dos trades"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.trades_file = self.project_root / "data" / "bancomat4_trades.json"
    
    def check_zscore_updates(self):
        """Verifica se Z-Scores estão sendo atualizados"""
        if not self.trades_file.exists():
            return []
        
        with open(self.trades_file, 'r') as f:
            trades = json.load(f)
        
        issues = []
        for trade in trades:
            entry_zscore = trade.get('entry_zscore', 0)
            current_zscore = trade.get('current_zscore', 0)
            
            if abs(entry_zscore - current_zscore) < 0.000001:
                entry_time = datetime.fromtimestamp(trade['entry_time'])
                age_minutes = (datetime.now() - entry_time).total_seconds() / 60
                
                if age_minutes > 5:  # Trade com mais de 5 minutos
                    issues.append({
                        'pair': trade['pair_name'],
                        'age_minutes': age_minutes,
                        'issue': 'Z-Score não atualizado'
                    })
        
        return issues
    
    def check_max_trades_limit(self, max_trades=5):
        """Verifica se está respeitando o limite de trades"""
        if not self.trades_file.exists():
            return None
        
        with open(self.trades_file, 'r') as f:
            trades = json.load(f)
        
        current_count = len(trades)
        
        return {
            'current_count': current_count,
            'max_trades': max_trades,
            'within_limit': current_count <= max_trades,
            'trades': [t['pair_name'] for t in trades]
        }
    
    def generate_report(self):
        """Gera relatório de status"""
        print("📊 RELATÓRIO DE STATUS DOS TRADES")
        print("=" * 50)
        
        # Verifica Z-Scores
        zscore_issues = self.check_zscore_updates()
        if zscore_issues:
            print("⚠️ PROBLEMAS COM Z-SCORE:")
            for issue in zscore_issues:
                print(f"   • {issue['pair']}: {issue['issue']} ({issue['age_minutes']:.1f} min)")
        else:
            print("✅ Z-Scores estão sendo atualizados corretamente")
        
        # Verifica limite de trades
        max_trades_status = self.check_max_trades_limit()
        if max_trades_status:
            print(f"\n📈 CONTROLE DE TRADES:")
            print(f"   • Trades ativos: {max_trades_status['current_count']}")
            print(f"   • Limite máximo: {max_trades_status['max_trades']}")
            if max_trades_status['within_limit']:
                print("   ✅ Dentro do limite")
            else:
                print("   ⚠️ ACIMA DO LIMITE!")
            print(f"   • Pares ativos: {', '.join(max_trades_status['trades'])}")

if __name__ == "__main__":
    monitor = TradeStatusMonitor()
    monitor.generate_report()
