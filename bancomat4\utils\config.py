#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Sistema de gerenciamento de configuração para Bancomat 4
- Carregamento de YAML
- Validação de configurações
- Config watchers
"""

import yaml
import os
from pathlib import Path
from typing import Dict, Any, Optional, Union, List
import threading
import time
from dataclasses import dataclass
from utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class ConfigValidationError(Exception):
    """Erro de validação de configuração"""
    field: str
    value: Any
    message: str


class ConfigManager:
    """Gerenciador de configuração do Bancomat 4"""
    
    def __init__(self, config_path: Optional[str] = None):
        # Define o diretório base do projeto
        self.project_dir = Path(__file__).parent.parent  # bancomat4/
        
        if config_path:
            self.config_path = config_path
        else:
            self.config_path = str(self.project_dir / "config" / "default_config.yaml")
        
        self.user_config_path = str(self.project_dir / "config" / "user_config.yaml")
        self.config = {}
        self.watchers = []
        self.lock = threading.RLock()
        self._load_config()
    
    def _load_config(self):
        """Carrega configuração dos arquivos YAML"""
        try:
            # Carrega configuração padrão
            default_config = self._load_yaml_file(self.config_path)
            
            # Carrega configuração do usuário se existir
            user_config = {}
            if os.path.exists(self.user_config_path):
                user_config = self._load_yaml_file(self.user_config_path)
            
            # Mescla configurações (user sobrescreve default)
            self.config = self._merge_configs(default_config, user_config)
            
            # Valida configuração
            self._validate_config()
            
            logger.info(f"Configuração carregada: {len(self.config)} seções")
            
        except Exception as e:
            logger.error(f"Erro ao carregar configuração: {e}")
            raise
    
    def _load_yaml_file(self, path: str) -> Dict[str, Any]:
        """Carrega arquivo YAML"""
        try:
            with open(path, 'r', encoding='utf-8') as f:
                content = yaml.safe_load(f)
                return content or {}
        except FileNotFoundError:
            logger.warning(f"Arquivo de configuração não encontrado: {path}")
            return {}
        except yaml.YAMLError as e:
            logger.error(f"Erro ao parsear YAML {path}: {e}")
            raise
    
    def _merge_configs(self, default: Dict[str, Any], user: Dict[str, Any]) -> Dict[str, Any]:
        """Mescla configurações recursivamente"""
        result = default.copy()
        
        for key, value in user.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_configs(result[key], value)
            else:
                result[key] = value
        
        return result
    
    def _validate_config(self):
        """Valida configuração carregada"""
        required_sections = ['mt5', 'trading', 'analysis', 'risk', 'signals']
        
        for section in required_sections:
            if section not in self.config:
                raise ConfigValidationError(section, None, f"Seção obrigatória '{section}' não encontrada")
        
        # Valida valores específicos
        self._validate_mt5_config()
        self._validate_trading_config()
        self._validate_analysis_config()
        self._validate_risk_config()
        self._validate_signals_config()
    
    def _validate_mt5_config(self):
        """Valida configuração MT5"""
        mt5_config = self.config.get('mt5', {})
        
        terminal_path = mt5_config.get('terminal_path', '')
        if not terminal_path:
            raise ConfigValidationError('mt5.terminal_path', terminal_path, "Caminho do terminal MT5 é obrigatório")
        
        if not os.path.exists(terminal_path):
            logger.warning(f"Caminho do terminal MT5 não encontrado: {terminal_path}")
    
    def _validate_trading_config(self):
        """Valida configuração de trading"""
        trading_config = self.config.get('trading', {})
        
        max_trades = trading_config.get('max_simultaneous_trades', 5)
        if max_trades < 1 or max_trades > 20:
            raise ConfigValidationError('trading.max_simultaneous_trades', max_trades, 
                                      "Número de trades simultâneos deve estar entre 1 e 20")
    
    def _validate_analysis_config(self):
        """Valida configuração de análise"""
        analysis_config = self.config.get('analysis', {})
        
        timeframes = analysis_config.get('timeframes', [])
        if not timeframes:
            raise ConfigValidationError('analysis.timeframes', timeframes, "Lista de timeframes não pode estar vazia")
        
        for tf in timeframes:
            if tf < 1 or tf > 1440:  # 1 minuto a 1 dia
                raise ConfigValidationError('analysis.timeframes', tf, 
                                          f"Timeframe {tf} fora do intervalo válido (1-1440 minutos)")
    
    def _validate_risk_config(self):
        """Valida configuração de risco"""
        risk_config = self.config.get('risk', {})
        
        risk_per_trade = risk_config.get('risk_per_trade', 1)
        if risk_per_trade <= 0 or risk_per_trade > 10:
            raise ConfigValidationError('risk.risk_per_trade', risk_per_trade, 
                                      "Risco por trade deve estar entre 0.1 e 10%")
    
    def _validate_signals_config(self):
        """Valida configuração de sinais"""
        signals_config = self.config.get('signals', {})
        
        min_score = signals_config.get('min_score', 65)
        if min_score < 0 or min_score > 100:
            raise ConfigValidationError('signals.min_score', min_score, 
                                      "Score mínimo deve estar entre 0 e 100")
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        Obtém valor de configuração usando notação de ponto
        
        Args:
            key: Chave da configuração (ex: 'mt5.terminal_path')
            default: Valor padrão se não encontrado
            
        Returns:
            Valor da configuração
        """
        with self.lock:
            keys = key.split('.')
            value = self.config
            
            for k in keys:
                if isinstance(value, dict) and k in value:
                    value = value[k]
                else:
                    return default
            
            return value
    
    def set(self, key: str, value: Any, save: bool = False):
        """
        Define valor de configuração
        
        Args:
            key: Chave da configuração
            value: Novo valor
            save: Se deve salvar no arquivo
        """
        with self.lock:
            keys = key.split('.')
            config_ref = self.config
            
            # Navega até o penúltimo nível
            for k in keys[:-1]:
                if k not in config_ref:
                    config_ref[k] = {}
                config_ref = config_ref[k]
            
            # Define o valor
            config_ref[keys[-1]] = value
            
            # Salva se solicitado
            if save:
                self.save_user_config()
            
            # Notifica watchers
            self._notify_watchers(key, value)
    
    def save_user_config(self):
        """Salva configuração do usuário"""
        try:
            # Cria diretório se não existir
            os.makedirs(os.path.dirname(self.user_config_path), exist_ok=True)
            
            with open(self.user_config_path, 'w', encoding='utf-8') as f:
                yaml.dump(self.config, f, default_flow_style=False, allow_unicode=True)
            
            logger.info(f"Configuração salva em {self.user_config_path}")
            
        except Exception as e:
            logger.error(f"Erro ao salvar configuração: {e}")
            raise
    
    def reload(self):
        """Recarrega configuração dos arquivos"""
        with self.lock:
            old_config = self.config.copy()
            self._load_config()
            
            # Notifica sobre mudanças
            self._notify_config_changed(old_config, self.config)
    
    def add_watcher(self, callback):
        """
        Adiciona callback para mudanças de configuração
        
        Args:
            callback: Função a ser chamada quando config mudar
        """
        self.watchers.append(callback)
    
    def remove_watcher(self, callback):
        """Remove watcher de configuração"""
        if callback in self.watchers:
            self.watchers.remove(callback)
    
    def _notify_watchers(self, key: str, value: Any):
        """Notifica watchers sobre mudança"""
        for watcher in self.watchers:
            try:
                watcher(key, value)
            except Exception as e:
                logger.error(f"Erro em watcher de configuração: {e}")
    
    def _notify_config_changed(self, old_config: Dict[str, Any], new_config: Dict[str, Any]):
        """Notifica sobre mudanças de configuração"""
        changes = self._find_config_changes(old_config, new_config)
        for key, (old_val, new_val) in changes.items():
            self._notify_watchers(key, new_val)
    
    def _find_config_changes(self, old: Dict[str, Any], new: Dict[str, Any], prefix: str = "") -> Dict[str, tuple]:
        """Encontra mudanças entre duas configurações"""
        changes = {}
        
        # Verifica valores alterados
        for key, value in new.items():
            full_key = f"{prefix}.{key}" if prefix else key
            
            if key not in old:
                changes[full_key] = (None, value)
            elif isinstance(value, dict) and isinstance(old[key], dict):
                changes.update(self._find_config_changes(old[key], value, full_key))
            elif old[key] != value:
                changes[full_key] = (old[key], value)
        
        # Verifica valores removidos
        for key in old:
            if key not in new:
                full_key = f"{prefix}.{key}" if prefix else key
                changes[full_key] = (old[key], None)
        
        return changes
    
    def get_section(self, section: str) -> Dict[str, Any]:
        """
        Obtém seção completa da configuração
        
        Args:
            section: Nome da seção
            
        Returns:
            Dicionário com a seção
        """
        return self.config.get(section, {}).copy()
    
    def validate_terminal_path(self) -> bool:
        """
        Valida se o caminho do terminal MT5 existe
        
        Returns:
            True se válido, False caso contrário
        """
        terminal_path = self.get('mt5.terminal_path', '')
        
        logger.info(f"🔍 Validando terminal MT5: {terminal_path}")
        
        if not terminal_path:
            logger.error("❌ Caminho do terminal MT5 não configurado")
            return False
        
        if not os.path.exists(terminal_path):
            logger.error(f"❌ Terminal MT5 não encontrado no caminho: {terminal_path}")
            return False
        
        # Verifica se é um arquivo executável
        if not terminal_path.lower().endswith('.exe'):
            logger.error(f"❌ Caminho não aponta para um executável: {terminal_path}")
            return False
        
        logger.info(f"✅ Terminal MT5 válido: {terminal_path}")
        return True


# Instância global do gerenciador de configuração
_config_manager = None


def get_config_manager(config_path: Optional[str] = None) -> ConfigManager:
    """
    Obtém instância global do gerenciador de configuração
    
    Args:
        config_path: Caminho do arquivo de configuração
        
    Returns:
        Instância do ConfigManager
    """
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager(config_path)
    return _config_manager 