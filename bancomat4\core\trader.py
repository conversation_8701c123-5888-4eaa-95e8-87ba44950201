#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Sistema de Trading Automático para Bancomat 4
- Execução automática de sinais gerados pelo analyzer
- Gestão completa de posições
- Monitoramento de P&L e saída automática
"""

import threading
import time
import uuid
import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path

try:
    import MetaTrader5 as mt5
except ImportError:
    mt5 = None

from utils.logger import get_logger
from utils.config import ConfigManager
from core.mt5_connector import MT5Connector
from core.analyzer import PairAnalyzer, Signal
from core.currency_exposure import CurrencyExposureManager

logger = get_logger(__name__)


@dataclass
class ActiveTrade:
    """Representa um trade ativo"""
    id: str
    pair_name: str
    symbol1: str
    symbol2: str
    
    # Dados de entrada
    entry_time: datetime
    entry_zscore: float
    signal_score: float
    direction: str  # 'buy_sell', 'sell_buy'
    
    # Tickets MT5
    symbol1_ticket: Optional[int] = None
    symbol2_ticket: Optional[int] = None
    symbol1_volume: float = 0.01
    symbol2_volume: float = 0.01
    symbol1_direction: str = ""  # 'BUY', 'SELL'
    symbol2_direction: str = ""  # 'BUY', 'SELL'
    
    # Preços
    entry_price1: float = 0.0
    entry_price2: float = 0.0
    current_price1: float = 0.0
    current_price2: float = 0.0
    
    # Análise atual
    beta: float = 1.0
    current_zscore: float = 0.0
    half_life: float = 24.0  # Half-life do par (em períodos)
    timeframe: int = 15      # Timeframe usado (em minutos)
    
    # P&L
    profit1: float = 0.0
    profit2: float = 0.0
    total_profit: float = 0.0
    
    # Controle
    last_update: datetime = None
    
    def __post_init__(self):
        if self.last_update is None:
            self.last_update = datetime.now()
    
    def get_timeout_seconds(self, timeout_multiplier: float = None) -> float:
        """Calcula timeout baseado no half-life"""
        if timeout_multiplier is None:
            timeout_multiplier = 3.0  # Padrão se não especificado
        # Timeout = half_life × multiplier × timeframe_minutes × 60_seconds
        return self.half_life * timeout_multiplier * self.timeframe * 60


class AutoTrader:
    """Sistema de trading automático"""
    
    def __init__(self, config: ConfigManager, mt5: MT5Connector, analyzer: PairAnalyzer = None):
        self.config = config
        self.mt5 = mt5
        self.analyzer = analyzer
        self.running = False
        
        # Configurações
        self.trading_config = config.get_section('trading')
        self.risk_config = config.get_section('risk')
        self.signals_config = config.get_section('signals')
        
        # Controle de trades
        self.active_trades: Dict[str, ActiveTrade] = {}
        self.trade_history: List[Dict[str, Any]] = []
        
        # Threading
        self.trading_thread = None
        self.monitoring_thread = None
        
        # Estatísticas
        self.stats = {
            'total_trades': 0,
            'profitable_trades': 0,
            'total_profit': 0.0,
            'max_drawdown': 0.0,
            'win_rate': 0.0
        }
        
        # Sistema de persistência de trades
        self.trades_file = Path("data") / "bancomat4_trades.json"
        self.trades_file.parent.mkdir(exist_ok=True)
        
        # Carrega trades existentes ao inicializar
        self.load_trades()
        
        # Sincroniza com posições abertas no MT5 apenas se MT5 estiver conectado
        if self.mt5 and self.mt5.check_connection():
            self.sync_with_mt5_positions()
        
        # NOVO: Sistema de controle de exposição por moeda
        self.exposure_manager = CurrencyExposureManager(config)
        
        # Carrega exposições anteriores
        self.exposure_manager.load_exposures()
        
        # NOVO: Conecta exposure manager ao analyzer se disponível
        if self.analyzer:
            self.analyzer.set_exposure_manager(self.exposure_manager)
        
        logger.info("🤖 Auto Trader inicializado")
        logger.info(f"✅ Trades carregados: {len(self.active_trades)}")
    
    def start_trading(self):
        """Inicia trading automático"""
        if self.running:
            logger.warning("Trading automático já está em execução")
            return
        
        if not self.analyzer:
            logger.warning("Analyzer não configurado - trading automático não pode iniciar")
            return
        
        if not self.analyzer.running:
            logger.error("Analyzer deve estar rodando antes do trader")
            return
        
        self.running = True
        
        # Thread principal de trading
        self.trading_thread = threading.Thread(target=self._trading_loop, daemon=True)
        self.trading_thread.start()
        
        # Thread de monitoramento
        self.monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitoring_thread.start()
        
        logger.info("🚀 Trading automático iniciado")
    
    def stop_trading(self):
        """Para trading automático"""
        self.running = False
        
        if self.trading_thread:
            self.trading_thread.join(timeout=5)
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=5)
        
        logger.info("🛑 Trading automático parado")
    
    def _trading_loop(self):
        """Loop principal de trading"""
        try:
            while self.running:
                try:
                    # Obtém sinais ativos do analyzer
                    active_signals = self.analyzer.get_active_signals()
                    
                    # Processa cada sinal
                    for signal in active_signals:
                        try:
                            if self._should_execute_signal(signal):
                                self._execute_signal(signal)
                        except Exception as e:
                            logger.error(f"Erro ao processar sinal {signal.pair_name}: {e}")
                    
                    # Aguarda próximo ciclo
                    time.sleep(5)  # Verifica sinais a cada 5 segundos
                    
                except Exception as e:
                    logger.error(f"Erro no loop de trading: {e}")
                    time.sleep(30)  # Espera mais em caso de erro
                    
        except Exception as e:
            logger.error(f"Erro fatal no loop de trading: {e}")
    
    def _monitoring_loop(self):
        """Loop de monitoramento de trades ativos"""
        try:
            while self.running:
                try:
                    # Atualiza trades ativos
                    self._update_active_trades()
                    
                    # Verifica condições de saída
                    self._check_exit_conditions()
                    
                    # Aguarda próximo ciclo
                    time.sleep(10)  # Monitora a cada 10 segundos
                    
                except Exception as e:
                    logger.error(f"Erro no monitoramento: {e}")
                    time.sleep(30)
                    
        except Exception as e:
            logger.error(f"Erro fatal no monitoramento: {e}")
    
    def _should_execute_signal(self, signal: Signal) -> bool:
        """Verifica se deve executar um sinal"""
        try:
            # Verifica se sinal é válido
            if signal.is_expired():
                return False
            
            # Verifica se já tem trade ativo para este par
            for trade in self.active_trades.values():
                if trade.pair_name == signal.pair_name:
                    return False
            
            
            # Verifica número máximo de trades com logging detalhado
            max_trades = self.trading_config.get('max_simultaneous_trades', 5)
            current_count = len(self.active_trades)
            
            if current_count >= max_trades:
                logger.info(f"🚫 Limite de trades atingido: {current_count}/{max_trades}")
                logger.debug(f"Trades ativos: {[t.pair_name for t in self.active_trades.values()]}")
                return False
            else:
                logger.debug(f"✅ Pode abrir trade: {current_count}/{max_trades}")
            
            # Verifica score mínimo
            min_score = self.signals_config.get('min_score', 60)
            if signal.score < min_score:
                return False
            
            # Verifica margem disponível
            if not self._check_margin_requirements(signal):
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Erro ao verificar sinal: {e}")
            return False
    
    def _check_margin_requirements(self, signal: Signal) -> bool:
        """Verifica se há margem suficiente"""
        try:
            account_info = self.mt5.get_account_info()
            if not account_info:
                return False
            
            # Margem mínima de segurança
            min_free_margin = self.risk_config.get('margin_safety_factor', 0.3) * account_info.margin_free
            required_margin = signal.margin_required
            
            return account_info.margin_free > (required_margin + min_free_margin)
            
        except Exception as e:
            logger.error(f"Erro ao verificar margem: {e}")
            return False
    
    def _execute_signal(self, signal: Signal) -> bool:
        """Executa um sinal automaticamente"""
        try:
            logger.info(f"🚀 Executando sinal automático: {signal.pair_name} - Score: {signal.score:.1f}")
            
            # Determina direção do trade
            if signal.signal_type == 'BUY':
                # Z-Score negativo: symbol1 barato, symbol2 caro
                symbol1_direction = 'BUY'
                symbol2_direction = 'SELL'
                direction = 'buy_sell'
            else:
                # Z-Score positivo: symbol1 caro, symbol2 barato
                symbol1_direction = 'SELL'
                symbol2_direction = 'BUY'
                direction = 'sell_buy'
            
            # Comentário para as ordens
            trade_id = str(uuid.uuid4())[:8]
            comment = f"B4_{signal.pair_name}_{trade_id}"
            
            # Executa primeira ordem
            logger.info(f"Abrindo {symbol1_direction} {signal.suggested_volume1} {signal.symbol1}")
            result1 = self.mt5.open_position(
                symbol=signal.symbol1,
                order_type=symbol1_direction,
                volume=signal.suggested_volume1,
                comment=comment
            )
            
            if not result1.get('success'):
                logger.error(f"Erro ao abrir posição {signal.symbol1}: {result1.get('error')}")
                return False
            
            # Executa segunda ordem
            logger.info(f"Abrindo {symbol2_direction} {signal.suggested_volume2} {signal.symbol2}")
            result2 = self.mt5.open_position(
                symbol=signal.symbol2,
                order_type=symbol2_direction,
                volume=signal.suggested_volume2,
                comment=comment
            )
            
            if not result2.get('success'):
                logger.error(f"Erro ao abrir posição {signal.symbol2}: {result2.get('error')}")
                
                # Fecha primeira posição se segunda falhou
                if result1.get('ticket'):
                    self.mt5.close_position(result1['ticket'])
                    logger.info(f"Posição {signal.symbol1} fechada devido a erro na segunda ordem")
                
                return False
            
            # Cria registro do trade ativo
            active_trade = ActiveTrade(
                id=trade_id,
                pair_name=signal.pair_name,
                symbol1=signal.symbol1,
                symbol2=signal.symbol2,
                entry_time=datetime.now(),
                entry_zscore=signal.z_score,
                signal_score=signal.score,
                direction=direction,
                symbol1_ticket=result1.get('ticket'),
                symbol2_ticket=result2.get('ticket'),
                symbol1_volume=signal.suggested_volume1,
                symbol2_volume=signal.suggested_volume2,
                symbol1_direction=symbol1_direction,
                symbol2_direction=symbol2_direction,
                entry_price1=result1.get('price', 0.0),
                entry_price2=result2.get('price', 0.0),
                beta=signal.beta,
                current_zscore=signal.z_score,
                half_life=signal.half_life,
                timeframe=signal.timeframe
            )
            
            # Adiciona à lista de trades ativos
            self.active_trades[trade_id] = active_trade
            
            # Atualiza estatísticas
            self.stats['total_trades'] += 1
            
            # Salva estado dos trades
            self.save_trades()
            
            logger.info(f"✅ Trade automático executado: {signal.pair_name} - ID: {trade_id}")
            logger.info(f"📊 Tickets: {signal.symbol1}={result1.get('ticket')}, {signal.symbol2}={result2.get('ticket')}")
            
            return True
            
        except Exception as e:
            logger.error(f"Erro ao executar sinal automático: {e}")
            return False
    
    def _update_active_trades(self):
        """Atualiza informações dos trades ativos"""
        try:
            for trade_id, trade in list(self.active_trades.items()):
                try:
                    # Obtém preços atuais
                    tick1 = self.mt5.get_tick_info(trade.symbol1)
                    tick2 = self.mt5.get_tick_info(trade.symbol2)
                    
                    if tick1 and tick2:
                        # Atualiza preços
                        if trade.symbol1_direction == 'BUY':
                            trade.current_price1 = tick1['bid']  # Preço para fechar BUY
                        else:
                            trade.current_price1 = tick1['ask']  # Preço para fechar SELL
                        
                        if trade.symbol2_direction == 'BUY':
                            trade.current_price2 = tick2['bid']  # Preço para fechar BUY
                        else:
                            trade.current_price2 = tick2['ask']  # Preço para fechar SELL
                        
                        # Cálculo de P&L CORRIGIDO - baseado em valor do pip real
                        symbol1_info = self.mt5.get_symbol_info(trade.symbol1)
                        symbol2_info = self.mt5.get_symbol_info(trade.symbol2)
                        
                        if symbol1_info and symbol2_info:
                            # Cálculo para symbol1
                            if trade.symbol1_direction == 'BUY':
                                price_diff1 = trade.current_price1 - trade.entry_price1
                            else:
                                price_diff1 = trade.entry_price1 - trade.current_price1
                            
                            # Cálculo para symbol2
                            if trade.symbol2_direction == 'BUY':
                                price_diff2 = trade.current_price2 - trade.entry_price2
                            else:
                                price_diff2 = trade.entry_price2 - trade.current_price2
                            
                            # P&L real baseado no valor do pip e volume
                            # Para forex: profit = (price_diff / symbol.point) * symbol.trade_tick_value * volume
                            if symbol1_info.get('point', 0) > 0:
                                trade.profit1 = (price_diff1 / symbol1_info['point']) * symbol1_info.get('trade_tick_value', 1.0) * trade.symbol1_volume
                            else:
                                trade.profit1 = 0.0
                                
                            if symbol2_info.get('point', 0) > 0:
                                trade.profit2 = (price_diff2 / symbol2_info['point']) * symbol2_info.get('trade_tick_value', 1.0) * trade.symbol2_volume
                            else:
                                trade.profit2 = 0.0
                            
                            trade.total_profit = trade.profit1 + trade.profit2
                        else:
                            # Fallback simples se não conseguir info do símbolo
                            if trade.symbol1_direction == 'BUY':
                                price_diff1 = trade.current_price1 - trade.entry_price1
                            else:
                                price_diff1 = trade.entry_price1 - trade.current_price1
                            
                            if trade.symbol2_direction == 'BUY':
                                price_diff2 = trade.current_price2 - trade.entry_price2
                            else:
                                price_diff2 = trade.entry_price2 - trade.current_price2
                            
                            # Estimativa conservadora
                            trade.profit1 = price_diff1 * trade.symbol1_volume * 10000  # Pip value approx
                            trade.profit2 = price_diff2 * trade.symbol2_volume * 10000
                            trade.total_profit = trade.profit1 + trade.profit2
                        
                        trade.last_update = datetime.now()
                        
                        # Obtém Z-Score atual se analyzer disponível
                        if self.analyzer:
                            current_analysis = self.analyzer.get_pair_analysis(trade.pair_name)
                            if current_analysis:
                                trade.current_zscore = current_analysis.z_score
                    
                except Exception as e:
                    logger.error(f"Erro ao atualizar trade {trade_id}: {e}")
                    
        except Exception as e:
            logger.error(f"Erro ao atualizar trades ativos: {e}")
    
    def _check_exit_conditions(self):
        """Verifica condições de saída para trades ativos - APENAS CRITÉRIOS ESTATÍSTICOS"""
        try:
            for trade_id, trade in list(self.active_trades.items()):
                try:
                    should_close = False
                    close_reason = ""
                    
                    # 1. PRINCIPAL: Reversão do Z-Score (objetivo do pair trading)
                    if abs(trade.current_zscore) <= 0.5:
                        should_close = True
                        close_reason = f"Z-Score reverteu para {trade.current_zscore:.3f}"
                    
                    # 2. PROTEÇÃO: Z-Score extremo demais (divergência excessiva)
                    elif abs(trade.current_zscore) >= 4.5:
                        should_close = True
                        close_reason = f"Z-Score extremo: {trade.current_zscore:.3f}"
                    
                    # 3. PROTEÇÃO: Tempo máximo (evita trades eternos)
                    elif (datetime.now() - trade.entry_time).total_seconds() > trade.get_timeout_seconds(
                        self.config.get_section('trading').get('timeout_multiplier', 3.0)
                    ):
                        should_close = True
                        timeout_hours = trade.get_timeout_seconds(
                            self.config.get_section('trading').get('timeout_multiplier', 3.0)
                        ) / 3600
                        close_reason = f"Timeout baseado em half-life ({timeout_hours:.1f}h)"
                    
                    if should_close:
                        logger.info(f"🚪 Fechando trade {trade.pair_name}: {close_reason}")
                        if self._close_trade(trade_id, close_reason):
                            logger.info(f"✅ Trade {trade.pair_name} fechado - P&L: ${trade.total_profit:.2f}")
                    
                except Exception as e:
                    logger.error(f"Erro ao verificar saída do trade {trade_id}: {e}")
                    
        except Exception as e:
            logger.error(f"Erro ao verificar condições de saída: {e}")
    
    def _close_trade(self, trade_id: str, reason: str) -> bool:
        """Fecha um trade ativo"""
        try:
            if trade_id not in self.active_trades:
                return False
            
            trade = self.active_trades[trade_id]
            
            # Fecha posições no MT5
            success1 = True
            success2 = True
            
            if trade.symbol1_ticket:
                result1 = self.mt5.close_position(trade.symbol1_ticket)
                success1 = result1.get('success', False)
                if not success1:
                    logger.error(f"Erro ao fechar {trade.symbol1}: {result1.get('error')}")
            
            if trade.symbol2_ticket:
                result2 = self.mt5.close_position(trade.symbol2_ticket)
                success2 = result2.get('success', False)
                if not success2:
                    logger.error(f"Erro ao fechar {trade.symbol2}: {result2.get('error')}")
            
            # Se pelo menos uma posição fechou, remove da lista ativa
            if success1 or success2:
                # Adiciona ao histórico
                trade_record = {
                    'id': trade_id,
                    'pair_name': trade.pair_name,
                    'entry_time': trade.entry_time,
                    'exit_time': datetime.now(),
                    'entry_zscore': trade.entry_zscore,
                    'exit_zscore': trade.current_zscore,
                    'profit': trade.total_profit,
                    'duration_minutes': (datetime.now() - trade.entry_time).total_seconds() / 60,
                    'close_reason': reason,
                    'signal_score': trade.signal_score
                }
                
                self.trade_history.append(trade_record)
                
                # Atualiza estatísticas
                if trade.total_profit > 0:
                    self.stats['profitable_trades'] += 1
                self.stats['total_profit'] += trade.total_profit
                self.stats['win_rate'] = (self.stats['profitable_trades'] / self.stats['total_trades']) * 100 if self.stats['total_trades'] > 0 else 0
                
                # Remove da lista ativa
                del self.active_trades[trade_id]
                
                # Salva estado dos trades
                self.save_trades()
                
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Erro ao fechar trade {trade_id}: {e}")
            return False
    
    def get_active_trades(self) -> List[ActiveTrade]:
        """Retorna lista de trades ativos"""
        return list(self.active_trades.values())
    
    def get_trade_history(self) -> List[Dict[str, Any]]:
        """Retorna histórico de trades"""
        return self.trade_history.copy()
    
    def get_stats(self) -> Dict[str, Any]:
        """Retorna estatísticas de trading"""
        stats = self.stats.copy()
        stats['active_trades'] = len(self.active_trades)
        return stats
    
    def close_all_trades(self, reason: str = "Manual close all") -> bool:
        """Fecha todos os trades ativos"""
        logger.info(f"🛑 Fechando todos os trades: {reason}")
        
        success_count = 0
        for trade_id in list(self.active_trades.keys()):
            if self._close_trade(trade_id, reason):
                success_count += 1
        
        logger.info(f"✅ {success_count} trades fechados")
        return success_count > 0

    def load_trades(self):
        """Carrega trades existentes do arquivo"""
        try:
            if self.trades_file.exists():
                with open(self.trades_file, 'r') as f:
                    trades = json.load(f)
                for trade_data in trades:
                    active_trade = ActiveTrade(
                        id=trade_data['id'],
                        pair_name=trade_data['pair_name'],
                        symbol1=trade_data['symbol1'],
                        symbol2=trade_data['symbol2'],
                        entry_time=datetime.fromtimestamp(trade_data['entry_time']),
                        entry_zscore=trade_data['entry_zscore'],
                        signal_score=trade_data['signal_score'],
                        direction=trade_data['direction'],
                        symbol1_ticket=trade_data['symbol1_ticket'],
                        symbol2_ticket=trade_data['symbol2_ticket'],
                        symbol1_volume=trade_data['symbol1_volume'],
                        symbol2_volume=trade_data['symbol2_volume'],
                        symbol1_direction=trade_data['symbol1_direction'],
                        symbol2_direction=trade_data['symbol2_direction'],
                        entry_price1=trade_data['entry_price1'],
                        entry_price2=trade_data['entry_price2'],
                        beta=trade_data['beta'],
                        current_zscore=trade_data['current_zscore'],
                        half_life=trade_data['half_life'],
                        timeframe=trade_data['timeframe']
                    )
                    self.active_trades[active_trade.id] = active_trade
                    
                logger.info(f"📂 {len(self.active_trades)} trades carregados de {self.trades_file}")
        except Exception as e:
            logger.error(f"Erro ao carregar trades: {e}")

    def save_trades(self):
        """Salva trades ativos no arquivo"""
        try:
            trades_data = []
            for trade in self.active_trades.values():
                trade_dict = {
                    'id': trade.id,
                    'pair_name': trade.pair_name,
                    'symbol1': trade.symbol1,
                    'symbol2': trade.symbol2,
                    'entry_time': trade.entry_time.timestamp(),
                    'entry_zscore': trade.entry_zscore,
                    'signal_score': trade.signal_score,
                    'direction': trade.direction,
                    'symbol1_ticket': trade.symbol1_ticket,
                    'symbol2_ticket': trade.symbol2_ticket,
                    'symbol1_volume': trade.symbol1_volume,
                    'symbol2_volume': trade.symbol2_volume,
                    'symbol1_direction': trade.symbol1_direction,
                    'symbol2_direction': trade.symbol2_direction,
                    'entry_price1': trade.entry_price1,
                    'entry_price2': trade.entry_price2,
                    'beta': trade.beta,
                    'current_zscore': trade.current_zscore,
                    'half_life': trade.half_life,
                    'timeframe': trade.timeframe
                }
                trades_data.append(trade_dict)
            
            with open(self.trades_file, 'w') as f:
                json.dump(trades_data, f, indent=2)
            
            logger.debug(f"💾 {len(trades_data)} trades salvos em {self.trades_file}")
            
        except Exception as e:
            logger.error(f"Erro ao salvar trades: {e}")

    def _update_current_zscores(self):
        """Atualiza o current_zscore de todos os trades ativos"""
        try:
            for trade_id, trade in self.active_trades.items():
                try:
                    # Calcula Z-Score atual
                    current_analysis = self.analyzer.analyze_pair(
                        trade.pair_name,
                        timeframe=trade.timeframe
                    )
                    
                    if current_analysis and 'z_score' in current_analysis:
                        old_zscore = trade.current_zscore
                        trade.current_zscore = current_analysis['z_score']
                        
                        # Log apenas se mudou significativamente
                        if abs(old_zscore - trade.current_zscore) > 0.01:
                            logger.debug(f"Z-Score atualizado para {trade.pair_name}: "
                                       f"{old_zscore:.3f} → {trade.current_zscore:.3f}")
                        
                        # Atualiza timestamp da última atualização
                        trade.last_update = datetime.now()
                        
                except Exception as e:
                    logger.error(f"Erro ao atualizar Z-Score do trade {trade_id}: {e}")
                    
        except Exception as e:
            logger.error(f"Erro ao atualizar Z-Scores: {e}")

    def sync_with_mt5_positions(self):
        """Sincroniza com posições abertas no MT5"""
        try:
            logger.info("🔄 Sincronizando trades com posições do MT5...")
            
            if not self.mt5.check_connection():
                logger.warning("MT5 não conectado - sincronização pulada")
                return
            
            # Obtém posições abertas
            positions_info = self.mt5.get_open_positions()
            if not positions_info:
                logger.info("Nenhuma posição aberta no MT5")
                return
            
            mt5_tickets = set()
            new_trades_found = 0
            
            for position in positions_info:
                ticket = position.get('ticket')
                if not ticket:
                    continue
                    
                mt5_tickets.add(ticket)
                
                # Verifica se já temos este trade registrado
                trade_found = False
                for trade in self.active_trades.values():
                    if trade.symbol1_ticket == ticket or trade.symbol2_ticket == ticket:
                        trade_found = True
                        # Atualiza dados do trade existente
                        if trade.symbol1_ticket == ticket:
                            trade.current_price1 = position.get('price_current', trade.current_price1)
                            trade.profit1 = position.get('profit', 0.0)
                        elif trade.symbol2_ticket == ticket:
                            trade.current_price2 = position.get('price_current', trade.current_price2)
                            trade.profit2 = position.get('profit', 0.0)
                        
                        trade.total_profit = trade.profit1 + trade.profit2
                        trade.last_update = datetime.now()
                        break
                
                if not trade_found:
                    # Criar novo trade a partir da posição do MT5
                    symbol = position.get('symbol', '')
                    if symbol:
                        trade_id = f"mt5_sync_{ticket}"
                        trade = ActiveTrade(
                            id=trade_id,
                            pair_name=f"{symbol}_SYNC",
                            symbol1=symbol,
                            symbol2="",
                            entry_time=datetime.fromtimestamp(position.get('time', time.time())),
                            entry_zscore=0.0,
                            signal_score=50.0,  # Score padrão para trades sincronizados
                            direction=position.get('type', 'BUY').lower(),
                            symbol1_ticket=ticket,
                            symbol2_ticket=None,
                            symbol1_volume=position.get('volume', 0.01),
                            symbol2_volume=0.0,
                            symbol1_direction=position.get('type', 'BUY'),
                            symbol2_direction="",
                            entry_price1=position.get('price_open', 0.0),
                            entry_price2=0.0,
                            current_price1=position.get('price_current', 0.0),
                            current_price2=0.0,
                            beta=1.0,
                            current_zscore=0.0,
                            half_life=24.0,
                            timeframe=15
                        )
                        
                        trade.profit1 = position.get('profit', 0.0)
                        trade.total_profit = trade.profit1
                        
                        self.active_trades[trade_id] = trade
                        new_trades_found += 1
                        
                        logger.info(f"📊 Trade sincronizado do MT5: {symbol} (ticket: {ticket})")
            
            # Remove trades que não têm mais posições abertas no MT5
            trades_to_remove = []
            for trade_id, trade in self.active_trades.items():
                if (trade.symbol1_ticket and trade.symbol1_ticket not in mt5_tickets) and \
                   (trade.symbol2_ticket and trade.symbol2_ticket not in mt5_tickets):
                    trades_to_remove.append(trade_id)
            
            for trade_id in trades_to_remove:
                trade = self.active_trades[trade_id]
                logger.info(f"🗑️ Removendo trade {trade.pair_name} - posições não encontradas no MT5")
                
                # Move para histórico
                trade_record = {
                    'id': trade_id,
                    'pair_name': trade.pair_name,
                    'entry_time': trade.entry_time,
                    'exit_time': datetime.now(),
                    'entry_zscore': trade.entry_zscore,
                    'exit_zscore': trade.current_zscore,
                    'profit': trade.total_profit,
                    'duration_minutes': (datetime.now() - trade.entry_time).total_seconds() / 60,
                    'close_reason': 'Sincronização MT5 - posição fechada externamente',
                    'signal_score': trade.signal_score
                }
                
                self.trade_history.append(trade_record)
                del self.active_trades[trade_id]
            
            # Salva estado atualizado
            self.save_trades()
            
            logger.info(f"✅ Sincronização concluída: {len(self.active_trades)} trades ativos, {new_trades_found} novos encontrados, {len(trades_to_remove)} removidos")
            
        except Exception as e:
            logger.error(f"Erro ao sincronizar com MT5: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}") 