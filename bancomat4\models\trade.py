#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Modelo de Trade para Bancomat 4
- Definição da estrutura de um trade
- Métodos de serialização
- Validações
"""

from dataclasses import dataclass, field
from datetime import datetime
from typing import Optional, Dict, Any, List
import uuid
import json


@dataclass
class Trade:
    """Modelo de um trade de pares"""
    
    # Identificação
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    pair_name: str = ""
    symbol1: str = ""
    symbol2: str = ""
    
    # Status
    status: str = "active"  # active, closed, error
    
    # Dados de entrada
    entry_time: datetime = field(default_factory=datetime.now)
    entry_zscore: float = 0.0
    entry_signal_score: float = 0.0
    direction: str = ""  # buy_sell, sell_buy
    
    # Ordens MT5
    symbol1_ticket: Optional[int] = None
    symbol2_ticket: Optional[int] = None
    symbol1_volume: float = 0.01
    symbol2_volume: float = 0.01
    symbol1_direction: str = ""  # BUY, SELL
    symbol2_direction: str = ""  # BUY, SELL
    
    # Preços
    entry_price1: float = 0.0
    entry_price2: float = 0.0
    current_price1: float = 0.0
    current_price2: float = 0.0
    exit_price1: Optional[float] = None
    exit_price2: Optional[float] = None
    
    # Análise
    beta: float = 1.0
    current_zscore: float = 0.0
    max_zscore: float = 0.0
    min_zscore: float = 0.0
    
    # P&L
    symbol1_profit: float = 0.0
    symbol2_profit: float = 0.0
    total_profit: float = 0.0
    max_profit: float = 0.0
    max_drawdown: float = 0.0
    
    # Margem
    symbol1_margin: float = 0.0
    symbol2_margin: float = 0.0
    total_margin: float = 0.0
    
    # Fechamento
    exit_time: Optional[datetime] = None
    exit_reason: str = ""
    exit_zscore: float = 0.0
    
    # Metadados
    comment: str = ""
    magic_number: int = 0
    trade_mode: str = "auto"  # auto, manual
    correlation: float = 0.0
    
    # Timestamps
    last_update: datetime = field(default_factory=datetime.now)
    created_at: datetime = field(default_factory=datetime.now)
    
    def __post_init__(self):
        """Validações pós-inicialização"""
        if not self.pair_name and self.symbol1 and self.symbol2:
            self.pair_name = f"{self.symbol1}_{self.symbol2}"
        
        # Atualiza timestamps se não definidos
        if isinstance(self.entry_time, str):
            self.entry_time = datetime.fromisoformat(self.entry_time.replace('Z', '+00:00'))
        if isinstance(self.last_update, str):
            self.last_update = datetime.fromisoformat(self.last_update.replace('Z', '+00:00'))
        if isinstance(self.created_at, str):
            self.created_at = datetime.fromisoformat(self.created_at.replace('Z', '+00:00'))
        if self.exit_time and isinstance(self.exit_time, str):
            self.exit_time = datetime.fromisoformat(self.exit_time.replace('Z', '+00:00'))
    
    def update_prices(self, price1: float, price2: float):
        """
        Atualiza preços atuais
        
        Args:
            price1: Preço atual do símbolo 1
            price2: Preço atual do símbolo 2
        """
        self.current_price1 = price1
        self.current_price2 = price2
        self.last_update = datetime.now()
    
    def update_profit(self, profit1: float, profit2: float):
        """
        Atualiza valores de lucro
        
        Args:
            profit1: Lucro do símbolo 1
            profit2: Lucro do símbolo 2
        """
        self.symbol1_profit = profit1
        self.symbol2_profit = profit2
        self.total_profit = profit1 + profit2
        
        # Atualiza máximos
        if self.total_profit > self.max_profit:
            self.max_profit = self.total_profit
        
        # Atualiza drawdown
        if self.max_profit > 0:
            current_drawdown = (self.max_profit - self.total_profit) / self.max_profit * 100
            if current_drawdown > self.max_drawdown:
                self.max_drawdown = current_drawdown
        
        self.last_update = datetime.now()
    
    def update_zscore(self, zscore: float):
        """
        Atualiza Z-score atual
        
        Args:
            zscore: Novo valor de Z-score
        """
        self.current_zscore = zscore
        
        # Atualiza extremos
        if zscore > self.max_zscore:
            self.max_zscore = zscore
        if zscore < self.min_zscore:
            self.min_zscore = zscore
        
        self.last_update = datetime.now()
    
    def update_margin(self, margin1: float, margin2: float):
        """
        Atualiza valores de margem
        
        Args:
            margin1: Margem do símbolo 1
            margin2: Margem do símbolo 2
        """
        self.symbol1_margin = margin1
        self.symbol2_margin = margin2
        self.total_margin = margin1 + margin2
        self.last_update = datetime.now()
    
    def close_trade(self, exit_reason: str, exit_zscore: float = None,
                   exit_price1: float = None, exit_price2: float = None):
        """
        Fecha o trade
        
        Args:
            exit_reason: Motivo do fechamento
            exit_zscore: Z-score no fechamento
            exit_price1: Preço de saída do símbolo 1
            exit_price2: Preço de saída do símbolo 2
        """
        self.status = "closed"
        self.exit_time = datetime.now()
        self.exit_reason = exit_reason
        
        if exit_zscore is not None:
            self.exit_zscore = exit_zscore
        else:
            self.exit_zscore = self.current_zscore
        
        if exit_price1 is not None:
            self.exit_price1 = exit_price1
        if exit_price2 is not None:
            self.exit_price2 = exit_price2
        
        self.last_update = datetime.now()
    
    def get_duration(self) -> Optional[float]:
        """
        Calcula duração do trade em segundos
        
        Returns:
            Duração em segundos ou None se ainda ativo
        """
        if self.exit_time:
            return (self.exit_time - self.entry_time).total_seconds()
        else:
            return (datetime.now() - self.entry_time).total_seconds()
    
    def get_duration_formatted(self) -> str:
        """
        Retorna duração formatada
        
        Returns:
            String com duração formatada
        """
        duration = self.get_duration()
        if duration is None:
            return "N/A"
        
        hours = int(duration // 3600)
        minutes = int((duration % 3600) // 60)
        seconds = int(duration % 60)
        
        if hours > 0:
            return f"{hours}h {minutes}m {seconds}s"
        elif minutes > 0:
            return f"{minutes}m {seconds}s"
        else:
            return f"{seconds}s"
    
    def get_pnl_percentage(self) -> float:
        """
        Calcula P&L em percentual baseado na margem
        
        Returns:
            P&L em percentual
        """
        if self.total_margin > 0:
            return (self.total_profit / self.total_margin) * 100
        return 0.0
    
    def is_profitable(self) -> bool:
        """
        Verifica se o trade é lucrativo
        
        Returns:
            True se lucrativo, False caso contrário
        """
        return self.total_profit > 0
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Converte para dicionário para serialização
        
        Returns:
            Dicionário com dados do trade
        """
        data = {}
        for key, value in self.__dict__.items():
            if isinstance(value, datetime):
                data[key] = value.isoformat()
            else:
                data[key] = value
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Trade':
        """
        Cria instância a partir de dicionário
        
        Args:
            data: Dicionário com dados do trade
            
        Returns:
            Instância de Trade
        """
        # Converte timestamps
        for field_name in ['entry_time', 'exit_time', 'last_update', 'created_at']:
            if field_name in data and isinstance(data[field_name], str):
                try:
                    data[field_name] = datetime.fromisoformat(data[field_name].replace('Z', '+00:00'))
                except ValueError:
                    data[field_name] = datetime.now()
        
        return cls(**data)
    
    def to_json(self) -> str:
        """
        Converte para JSON
        
        Returns:
            String JSON
        """
        return json.dumps(self.to_dict(), ensure_ascii=False, indent=2)
    
    @classmethod
    def from_json(cls, json_str: str) -> 'Trade':
        """
        Cria instância a partir de JSON
        
        Args:
            json_str: String JSON
            
        Returns:
            Instância de Trade
        """
        data = json.loads(json_str)
        return cls.from_dict(data)
    
    def __str__(self) -> str:
        """String representation"""
        status_emoji = "🟢" if self.status == "active" else "🔴" if self.status == "closed" else "⚠️"
        profit_emoji = "📈" if self.total_profit > 0 else "📉" if self.total_profit < 0 else "➖"
        
        return (f"{status_emoji} {self.pair_name} | "
                f"Z-Score: {self.current_zscore:.2f} | "
                f"{profit_emoji} P&L: {self.total_profit:.2f} | "
                f"Duração: {self.get_duration_formatted()}")
    
    def __repr__(self) -> str:
        """Representation for debugging"""
        return (f"Trade(id='{self.id[:8]}...', pair='{self.pair_name}', "
                f"status='{self.status}', profit={self.total_profit:.2f})")


class TradeStats:
    """Classe para estatísticas de trades"""
    
    def __init__(self, trades: List[Trade]):
        self.trades = trades
    
    def get_total_trades(self) -> int:
        """Total de trades"""
        return len(self.trades)
    
    def get_active_trades(self) -> List[Trade]:
        """Trades ativos"""
        return [t for t in self.trades if t.status == "active"]
    
    def get_closed_trades(self) -> List[Trade]:
        """Trades fechados"""
        return [t for t in self.trades if t.status == "closed"]
    
    def get_profitable_trades(self) -> List[Trade]:
        """Trades lucrativos"""
        return [t for t in self.get_closed_trades() if t.total_profit > 0]
    
    def get_losing_trades(self) -> List[Trade]:
        """Trades perdedores"""
        return [t for t in self.get_closed_trades() if t.total_profit < 0]
    
    def get_win_rate(self) -> float:
        """Taxa de acerto"""
        closed = self.get_closed_trades()
        if not closed:
            return 0.0
        profitable = self.get_profitable_trades()
        return (len(profitable) / len(closed)) * 100
    
    def get_total_profit(self) -> float:
        """Lucro total"""
        return sum(t.total_profit for t in self.get_closed_trades())
    
    def get_average_profit(self) -> float:
        """Lucro médio por trade"""
        closed = self.get_closed_trades()
        if not closed:
            return 0.0
        return self.get_total_profit() / len(closed)
    
    def get_best_trade(self) -> Optional[Trade]:
        """Melhor trade"""
        closed = self.get_closed_trades()
        if not closed:
            return None
        return max(closed, key=lambda t: t.total_profit)
    
    def get_worst_trade(self) -> Optional[Trade]:
        """Pior trade"""
        closed = self.get_closed_trades()
        if not closed:
            return None
        return min(closed, key=lambda t: t.total_profit)
    
    def get_current_total_margin(self) -> float:
        """Margem total atual (trades ativos)"""
        return sum(t.total_margin for t in self.get_active_trades())
    
    def get_current_total_profit(self) -> float:
        """Lucro total atual (trades ativos)"""
        return sum(t.total_profit for t in self.get_active_trades()) 