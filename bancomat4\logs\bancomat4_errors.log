2025-05-22 23:17:37,825 - core.analyzer - [31mERROR[0m - Erro ao encontrar símbolo correlacionado para SymbolInfo(name='AUDCAD', description='Australian Dollar vs Canadian Dollar', currency_base='AUD', currency_profit='CAD', volume_min=0.01, volume_max=100.0, volume_step=0.01, spread=9.00000000000345, trade_mode=4, is_forex=True): object of type 'SymbolInfo' has no len()
Module: analyzer - Function: _find_correlated_symbol - Line: 297
C:\Bancomat\bancomat4\core\analyzer.py

2025-05-22 23:20:45,674 - core.analyzer - [31mERROR[0m - Erro ao atualizar análises: 'list' object has no attribute 'keys'
Module: analyzer - Function: _update_analysis - Line: 215
C:\Bancomat\bancomat4\core\analyzer.py

2025-05-22 23:20:55,679 - core.analyzer - [31mERROR[0m - Erro ao atualizar análises: 'list' object has no attribute 'keys'
Module: analyzer - Function: _update_analysis - Line: 215
C:\Bancomat\bancomat4\core\analyzer.py

2025-05-22 23:32:25,544 - core.analyzer - [31mERROR[0m - Erro ao encontrar símbolo correlacionado para SymbolInfo(name='AUDCAD', description='Australian Dollar vs Canadian Dollar', currency_base='AUD', currency_profit='CAD', volume_min=0.01, volume_max=100.0, volume_step=0.01, spread=0, trade_mode=4, is_forex=True): 'SymbolInfo' object is not subscriptable
Module: analyzer - Function: _find_correlated_symbol - Line: 297
C:\Bancomat\bancomat4\core\analyzer.py

2025-05-22 23:32:25,545 - core.analyzer - [31mERROR[0m - Erro ao encontrar símbolo correlacionado para SymbolInfo(name='AUDCHF', description='Australian Dollar vs Swiss Franc', currency_base='AUD', currency_profit='CHF', volume_min=0.01, volume_max=100.0, volume_step=0.01, spread=0, trade_mode=4, is_forex=True): 'SymbolInfo' object is not subscriptable
Module: analyzer - Function: _find_correlated_symbol - Line: 297
C:\Bancomat\bancomat4\core\analyzer.py

2025-05-22 23:32:25,545 - core.analyzer - [31mERROR[0m - Erro ao encontrar símbolo correlacionado para SymbolInfo(name='AUDHUF', description='Australian Dollar vs Hungarian Forint', currency_base='AUD', currency_profit='HUF', volume_min=0.01, volume_max=100.0, volume_step=0.01, spread=0, trade_mode=4, is_forex=True): 'SymbolInfo' object is not subscriptable
Module: analyzer - Function: _find_correlated_symbol - Line: 297
C:\Bancomat\bancomat4\core\analyzer.py

2025-05-22 23:32:25,546 - core.analyzer - [31mERROR[0m - Erro ao encontrar símbolo correlacionado para SymbolInfo(name='AUDJPY', description='Australian Dollar vs Japanese Yen', currency_base='AUD', currency_profit='JPY', volume_min=0.01, volume_max=100.0, volume_step=0.01, spread=0, trade_mode=4, is_forex=True): 'SymbolInfo' object is not subscriptable
Module: analyzer - Function: _find_correlated_symbol - Line: 297
C:\Bancomat\bancomat4\core\analyzer.py

2025-05-22 23:32:25,546 - core.analyzer - [31mERROR[0m - Erro ao encontrar símbolo correlacionado para SymbolInfo(name='AUDNZD', description='Australian Dollar vs New Zealand Dollar', currency_base='AUD', currency_profit='NZD', volume_min=0.01, volume_max=100.0, volume_step=0.01, spread=0, trade_mode=4, is_forex=True): 'SymbolInfo' object is not subscriptable
Module: analyzer - Function: _find_correlated_symbol - Line: 297
C:\Bancomat\bancomat4\core\analyzer.py

2025-05-22 23:32:25,546 - core.analyzer - [31mERROR[0m - Erro ao encontrar símbolo correlacionado para SymbolInfo(name='AUDSGD', description='Australian Dollar vs Singaporean Dollar', currency_base='AUD', currency_profit='SGD', volume_min=0.01, volume_max=100.0, volume_step=0.01, spread=0, trade_mode=4, is_forex=True): 'SymbolInfo' object is not subscriptable
Module: analyzer - Function: _find_correlated_symbol - Line: 297
C:\Bancomat\bancomat4\core\analyzer.py

2025-05-22 23:32:25,547 - core.analyzer - [31mERROR[0m - Erro ao encontrar símbolo correlacionado para SymbolInfo(name='AUDUSD', description='Australian Dollar vs US Dollar', currency_base='AUD', currency_profit='USD', volume_min=0.01, volume_max=100.0, volume_step=0.01, spread=0, trade_mode=4, is_forex=True): 'SymbolInfo' object is not subscriptable
Module: analyzer - Function: _find_correlated_symbol - Line: 297
C:\Bancomat\bancomat4\core\analyzer.py

2025-05-22 23:32:25,547 - core.analyzer - [31mERROR[0m - Erro ao encontrar símbolo correlacionado para SymbolInfo(name='AUS200', description='Australia 200 Index', currency_base='AUD', currency_profit='AUD', volume_min=0.01, volume_max=100.0, volume_step=0.01, spread=0, trade_mode=4, is_forex=True): 'SymbolInfo' object is not subscriptable
Module: analyzer - Function: _find_correlated_symbol - Line: 297
C:\Bancomat\bancomat4\core\analyzer.py

2025-05-22 23:32:25,548 - core.analyzer - [31mERROR[0m - Erro ao encontrar símbolo correlacionado para SymbolInfo(name='BCHUSD', description='Bitcoin Cash vs US Dollar', currency_base='USD', currency_profit='USD', volume_min=0.01, volume_max=100.0, volume_step=0.01, spread=0, trade_mode=4, is_forex=True): 'SymbolInfo' object is not subscriptable
Module: analyzer - Function: _find_correlated_symbol - Line: 297
C:\Bancomat\bancomat4\core\analyzer.py

2025-05-22 23:32:25,548 - core.analyzer - [31mERROR[0m - Erro ao encontrar símbolo correlacionado para SymbolInfo(name='BTCUSD', description='Bitcoin vs US Dollar', currency_base='USD', currency_profit='USD', volume_min=0.01, volume_max=100.0, volume_step=0.01, spread=0, trade_mode=4, is_forex=True): 'SymbolInfo' object is not subscriptable
Module: analyzer - Function: _find_correlated_symbol - Line: 297
C:\Bancomat\bancomat4\core\analyzer.py

2025-05-22 23:32:25,549 - core.analyzer - [31mERROR[0m - Erro ao encontrar símbolo correlacionado para SymbolInfo(name='CADCHF', description='Canadian Dollar vs Swiss Franc', currency_base='CAD', currency_profit='CHF', volume_min=0.01, volume_max=100.0, volume_step=0.01, spread=0, trade_mode=4, is_forex=True): 'SymbolInfo' object is not subscriptable
Module: analyzer - Function: _find_correlated_symbol - Line: 297
C:\Bancomat\bancomat4\core\analyzer.py

2025-05-22 23:32:25,549 - core.analyzer - [31mERROR[0m - Erro ao encontrar símbolo correlacionado para SymbolInfo(name='CADJPY', description='Canadian Dollar vs Japanese Yen', currency_base='CAD', currency_profit='JPY', volume_min=0.01, volume_max=100.0, volume_step=0.01, spread=0, trade_mode=4, is_forex=True): 'SymbolInfo' object is not subscriptable
Module: analyzer - Function: _find_correlated_symbol - Line: 297
C:\Bancomat\bancomat4\core\analyzer.py

2025-05-22 23:32:25,549 - core.analyzer - [31mERROR[0m - Erro ao encontrar símbolo correlacionado para SymbolInfo(name='CHFDKK', description='Swiss Franc vs Danish Krone', currency_base='CHF', currency_profit='DKK', volume_min=0.01, volume_max=100.0, volume_step=0.01, spread=0, trade_mode=4, is_forex=True): 'SymbolInfo' object is not subscriptable
Module: analyzer - Function: _find_correlated_symbol - Line: 297
C:\Bancomat\bancomat4\core\analyzer.py

2025-05-22 23:32:25,550 - core.analyzer - [31mERROR[0m - Erro ao encontrar símbolo correlacionado para SymbolInfo(name='CHFHUF', description='Swiss Franc vs Hungarian Forint', currency_base='CHF', currency_profit='HUF', volume_min=0.01, volume_max=100.0, volume_step=0.01, spread=0, trade_mode=4, is_forex=True): 'SymbolInfo' object is not subscriptable
Module: analyzer - Function: _find_correlated_symbol - Line: 297
C:\Bancomat\bancomat4\core\analyzer.py

2025-05-22 23:32:25,550 - core.analyzer - [31mERROR[0m - Erro ao encontrar símbolo correlacionado para SymbolInfo(name='CHFJPY', description='Swiss Franc vs Japanese Yen', currency_base='CHF', currency_profit='JPY', volume_min=0.01, volume_max=100.0, volume_step=0.01, spread=0, trade_mode=4, is_forex=True): 'SymbolInfo' object is not subscriptable
Module: analyzer - Function: _find_correlated_symbol - Line: 297
C:\Bancomat\bancomat4\core\analyzer.py

2025-05-22 23:32:25,550 - core.analyzer - [31mERROR[0m - Erro ao encontrar símbolo correlacionado para SymbolInfo(name='CHFNOK', description='Swiss Franc vs Norwegian Krone', currency_base='CHF', currency_profit='NOK', volume_min=0.01, volume_max=100.0, volume_step=0.01, spread=0, trade_mode=4, is_forex=True): 'SymbolInfo' object is not subscriptable
Module: analyzer - Function: _find_correlated_symbol - Line: 297
C:\Bancomat\bancomat4\core\analyzer.py

2025-05-22 23:32:25,552 - core.analyzer - [31mERROR[0m - Erro ao encontrar símbolo correlacionado para SymbolInfo(name='CHFPLN', description='Swiss Franc vs Polish Zloty', currency_base='CHF', currency_profit='PLN', volume_min=0.01, volume_max=100.0, volume_step=0.01, spread=0, trade_mode=4, is_forex=True): 'SymbolInfo' object is not subscriptable
Module: analyzer - Function: _find_correlated_symbol - Line: 297
C:\Bancomat\bancomat4\core\analyzer.py

2025-05-22 23:32:25,552 - core.analyzer - [31mERROR[0m - Erro ao encontrar símbolo correlacionado para SymbolInfo(name='CHFSEK', description='Swiss Franc vs Swedish Krona', currency_base='CHF', currency_profit='SEK', volume_min=0.01, volume_max=100.0, volume_step=0.01, spread=0, trade_mode=4, is_forex=True): 'SymbolInfo' object is not subscriptable
Module: analyzer - Function: _find_correlated_symbol - Line: 297
C:\Bancomat\bancomat4\core\analyzer.py

2025-05-22 23:32:25,552 - core.analyzer - [31mERROR[0m - Erro ao encontrar símbolo correlacionado para SymbolInfo(name='CHFSGD', description='Swiss Franc vs Singaporean Dollar', currency_base='CHF', currency_profit='SGD', volume_min=0.01, volume_max=100.0, volume_step=0.01, spread=0, trade_mode=4, is_forex=True): 'SymbolInfo' object is not subscriptable
Module: analyzer - Function: _find_correlated_symbol - Line: 297
C:\Bancomat\bancomat4\core\analyzer.py

2025-05-22 23:32:25,553 - core.analyzer - [31mERROR[0m - Erro ao encontrar símbolo correlacionado para SymbolInfo(name='COFARA', description='Arabica Coffee vs US Dollar', currency_base='USD', currency_profit='USD', volume_min=1.0, volume_max=100.0, volume_step=1.0, spread=0, trade_mode=4, is_forex=True): 'SymbolInfo' object is not subscriptable
Module: analyzer - Function: _find_correlated_symbol - Line: 297
C:\Bancomat\bancomat4\core\analyzer.py

2025-05-22 23:32:35,557 - core.analyzer - [31mERROR[0m - Erro ao encontrar símbolo correlacionado para SymbolInfo(name='AUDCAD', description='Australian Dollar vs Canadian Dollar', currency_base='AUD', currency_profit='CAD', volume_min=0.01, volume_max=100.0, volume_step=0.01, spread=0, trade_mode=4, is_forex=True): 'SymbolInfo' object is not subscriptable
Module: analyzer - Function: _find_correlated_symbol - Line: 297
C:\Bancomat\bancomat4\core\analyzer.py

2025-05-22 23:32:35,558 - core.analyzer - [31mERROR[0m - Erro ao encontrar símbolo correlacionado para SymbolInfo(name='AUDCHF', description='Australian Dollar vs Swiss Franc', currency_base='AUD', currency_profit='CHF', volume_min=0.01, volume_max=100.0, volume_step=0.01, spread=0, trade_mode=4, is_forex=True): 'SymbolInfo' object is not subscriptable
Module: analyzer - Function: _find_correlated_symbol - Line: 297
C:\Bancomat\bancomat4\core\analyzer.py

2025-05-22 23:32:35,559 - core.analyzer - [31mERROR[0m - Erro ao encontrar símbolo correlacionado para SymbolInfo(name='AUDHUF', description='Australian Dollar vs Hungarian Forint', currency_base='AUD', currency_profit='HUF', volume_min=0.01, volume_max=100.0, volume_step=0.01, spread=0, trade_mode=4, is_forex=True): 'SymbolInfo' object is not subscriptable
Module: analyzer - Function: _find_correlated_symbol - Line: 297
C:\Bancomat\bancomat4\core\analyzer.py

2025-05-22 23:32:35,559 - core.analyzer - [31mERROR[0m - Erro ao encontrar símbolo correlacionado para SymbolInfo(name='AUDJPY', description='Australian Dollar vs Japanese Yen', currency_base='AUD', currency_profit='JPY', volume_min=0.01, volume_max=100.0, volume_step=0.01, spread=0, trade_mode=4, is_forex=True): 'SymbolInfo' object is not subscriptable
Module: analyzer - Function: _find_correlated_symbol - Line: 297
C:\Bancomat\bancomat4\core\analyzer.py

2025-05-22 23:32:35,559 - core.analyzer - [31mERROR[0m - Erro ao encontrar símbolo correlacionado para SymbolInfo(name='AUDNZD', description='Australian Dollar vs New Zealand Dollar', currency_base='AUD', currency_profit='NZD', volume_min=0.01, volume_max=100.0, volume_step=0.01, spread=0, trade_mode=4, is_forex=True): 'SymbolInfo' object is not subscriptable
Module: analyzer - Function: _find_correlated_symbol - Line: 297
C:\Bancomat\bancomat4\core\analyzer.py

2025-05-22 23:32:35,560 - core.analyzer - [31mERROR[0m - Erro ao encontrar símbolo correlacionado para SymbolInfo(name='AUDSGD', description='Australian Dollar vs Singaporean Dollar', currency_base='AUD', currency_profit='SGD', volume_min=0.01, volume_max=100.0, volume_step=0.01, spread=0, trade_mode=4, is_forex=True): 'SymbolInfo' object is not subscriptable
Module: analyzer - Function: _find_correlated_symbol - Line: 297
C:\Bancomat\bancomat4\core\analyzer.py

2025-05-22 23:32:35,560 - core.analyzer - [31mERROR[0m - Erro ao encontrar símbolo correlacionado para SymbolInfo(name='AUDUSD', description='Australian Dollar vs US Dollar', currency_base='AUD', currency_profit='USD', volume_min=0.01, volume_max=100.0, volume_step=0.01, spread=0, trade_mode=4, is_forex=True): 'SymbolInfo' object is not subscriptable
Module: analyzer - Function: _find_correlated_symbol - Line: 297
C:\Bancomat\bancomat4\core\analyzer.py

2025-05-22 23:32:35,562 - core.analyzer - [31mERROR[0m - Erro ao encontrar símbolo correlacionado para SymbolInfo(name='AUS200', description='Australia 200 Index', currency_base='AUD', currency_profit='AUD', volume_min=0.01, volume_max=100.0, volume_step=0.01, spread=0, trade_mode=4, is_forex=True): 'SymbolInfo' object is not subscriptable
Module: analyzer - Function: _find_correlated_symbol - Line: 297
C:\Bancomat\bancomat4\core\analyzer.py

2025-05-22 23:32:35,562 - core.analyzer - [31mERROR[0m - Erro ao encontrar símbolo correlacionado para SymbolInfo(name='BCHUSD', description='Bitcoin Cash vs US Dollar', currency_base='USD', currency_profit='USD', volume_min=0.01, volume_max=100.0, volume_step=0.01, spread=0, trade_mode=4, is_forex=True): 'SymbolInfo' object is not subscriptable
Module: analyzer - Function: _find_correlated_symbol - Line: 297
C:\Bancomat\bancomat4\core\analyzer.py

2025-05-22 23:32:35,563 - core.analyzer - [31mERROR[0m - Erro ao encontrar símbolo correlacionado para SymbolInfo(name='BTCUSD', description='Bitcoin vs US Dollar', currency_base='USD', currency_profit='USD', volume_min=0.01, volume_max=100.0, volume_step=0.01, spread=0, trade_mode=4, is_forex=True): 'SymbolInfo' object is not subscriptable
Module: analyzer - Function: _find_correlated_symbol - Line: 297
C:\Bancomat\bancomat4\core\analyzer.py

2025-05-22 23:32:35,563 - core.analyzer - [31mERROR[0m - Erro ao encontrar símbolo correlacionado para SymbolInfo(name='CADCHF', description='Canadian Dollar vs Swiss Franc', currency_base='CAD', currency_profit='CHF', volume_min=0.01, volume_max=100.0, volume_step=0.01, spread=0, trade_mode=4, is_forex=True): 'SymbolInfo' object is not subscriptable
Module: analyzer - Function: _find_correlated_symbol - Line: 297
C:\Bancomat\bancomat4\core\analyzer.py

2025-05-22 23:32:35,564 - core.analyzer - [31mERROR[0m - Erro ao encontrar símbolo correlacionado para SymbolInfo(name='CADJPY', description='Canadian Dollar vs Japanese Yen', currency_base='CAD', currency_profit='JPY', volume_min=0.01, volume_max=100.0, volume_step=0.01, spread=0, trade_mode=4, is_forex=True): 'SymbolInfo' object is not subscriptable
Module: analyzer - Function: _find_correlated_symbol - Line: 297
C:\Bancomat\bancomat4\core\analyzer.py

2025-05-22 23:32:35,564 - core.analyzer - [31mERROR[0m - Erro ao encontrar símbolo correlacionado para SymbolInfo(name='CHFDKK', description='Swiss Franc vs Danish Krone', currency_base='CHF', currency_profit='DKK', volume_min=0.01, volume_max=100.0, volume_step=0.01, spread=0, trade_mode=4, is_forex=True): 'SymbolInfo' object is not subscriptable
Module: analyzer - Function: _find_correlated_symbol - Line: 297
C:\Bancomat\bancomat4\core\analyzer.py

2025-05-22 23:32:35,565 - core.analyzer - [31mERROR[0m - Erro ao encontrar símbolo correlacionado para SymbolInfo(name='CHFHUF', description='Swiss Franc vs Hungarian Forint', currency_base='CHF', currency_profit='HUF', volume_min=0.01, volume_max=100.0, volume_step=0.01, spread=0, trade_mode=4, is_forex=True): 'SymbolInfo' object is not subscriptable
Module: analyzer - Function: _find_correlated_symbol - Line: 297
C:\Bancomat\bancomat4\core\analyzer.py

2025-05-22 23:32:35,565 - core.analyzer - [31mERROR[0m - Erro ao encontrar símbolo correlacionado para SymbolInfo(name='CHFJPY', description='Swiss Franc vs Japanese Yen', currency_base='CHF', currency_profit='JPY', volume_min=0.01, volume_max=100.0, volume_step=0.01, spread=0, trade_mode=4, is_forex=True): 'SymbolInfo' object is not subscriptable
Module: analyzer - Function: _find_correlated_symbol - Line: 297
C:\Bancomat\bancomat4\core\analyzer.py

2025-05-22 23:32:35,566 - core.analyzer - [31mERROR[0m - Erro ao encontrar símbolo correlacionado para SymbolInfo(name='CHFNOK', description='Swiss Franc vs Norwegian Krone', currency_base='CHF', currency_profit='NOK', volume_min=0.01, volume_max=100.0, volume_step=0.01, spread=0, trade_mode=4, is_forex=True): 'SymbolInfo' object is not subscriptable
Module: analyzer - Function: _find_correlated_symbol - Line: 297
C:\Bancomat\bancomat4\core\analyzer.py

2025-05-22 23:32:35,566 - core.analyzer - [31mERROR[0m - Erro ao encontrar símbolo correlacionado para SymbolInfo(name='CHFPLN', description='Swiss Franc vs Polish Zloty', currency_base='CHF', currency_profit='PLN', volume_min=0.01, volume_max=100.0, volume_step=0.01, spread=0, trade_mode=4, is_forex=True): 'SymbolInfo' object is not subscriptable
Module: analyzer - Function: _find_correlated_symbol - Line: 297
C:\Bancomat\bancomat4\core\analyzer.py

2025-05-22 23:32:35,566 - core.analyzer - [31mERROR[0m - Erro ao encontrar símbolo correlacionado para SymbolInfo(name='CHFSEK', description='Swiss Franc vs Swedish Krona', currency_base='CHF', currency_profit='SEK', volume_min=0.01, volume_max=100.0, volume_step=0.01, spread=0, trade_mode=4, is_forex=True): 'SymbolInfo' object is not subscriptable
Module: analyzer - Function: _find_correlated_symbol - Line: 297
C:\Bancomat\bancomat4\core\analyzer.py

2025-05-22 23:32:35,567 - core.analyzer - [31mERROR[0m - Erro ao encontrar símbolo correlacionado para SymbolInfo(name='CHFSGD', description='Swiss Franc vs Singaporean Dollar', currency_base='CHF', currency_profit='SGD', volume_min=0.01, volume_max=100.0, volume_step=0.01, spread=0, trade_mode=4, is_forex=True): 'SymbolInfo' object is not subscriptable
Module: analyzer - Function: _find_correlated_symbol - Line: 297
C:\Bancomat\bancomat4\core\analyzer.py

2025-05-22 23:32:35,567 - core.analyzer - [31mERROR[0m - Erro ao encontrar símbolo correlacionado para SymbolInfo(name='COFARA', description='Arabica Coffee vs US Dollar', currency_base='USD', currency_profit='USD', volume_min=1.0, volume_max=100.0, volume_step=1.0, spread=0, trade_mode=4, is_forex=True): 'SymbolInfo' object is not subscriptable
Module: analyzer - Function: _find_correlated_symbol - Line: 297
C:\Bancomat\bancomat4\core\analyzer.py

2025-05-22 23:36:59,836 - core.position_sizing - [31mERROR[0m - Erro no cálculo proporcional: 'AccountInfo' object has no attribute 'get'
Module: position_sizing - Function: _calculate_proportional_volumes - Line: 230
C:\Bancomat\bancomat4\core\position_sizing.py

2025-05-22 23:37:26,369 - core.mt5_connector - [31mERROR[0m - Erro ao abrir posição: 10027 - AutoTrading disabled by client
Module: mt5_connector - Function: open_position - Line: 603
C:\Bancomat\bancomat4\core\mt5_connector.py

2025-05-22 23:37:26,370 - core.trader - [31mERROR[0m - Erro ao abrir posição BCHUSD: Erro ao abrir posição: 10027 - AutoTrading disabled by client
Module: trader - Function: _execute_signal - Line: 269
C:\Bancomat\bancomat4\core\trader.py

2025-05-22 23:37:31,379 - core.mt5_connector - [31mERROR[0m - Erro ao abrir posição: 10027 - AutoTrading disabled by client
Module: mt5_connector - Function: open_position - Line: 603
C:\Bancomat\bancomat4\core\mt5_connector.py

2025-05-22 23:37:31,379 - core.trader - [31mERROR[0m - Erro ao abrir posição BCHUSD: Erro ao abrir posição: 10027 - AutoTrading disabled by client
Module: trader - Function: _execute_signal - Line: 269
C:\Bancomat\bancomat4\core\trader.py

2025-05-22 23:37:36,390 - core.mt5_connector - [31mERROR[0m - Erro ao abrir posição: 10027 - AutoTrading disabled by client
Module: mt5_connector - Function: open_position - Line: 603
C:\Bancomat\bancomat4\core\mt5_connector.py

2025-05-22 23:37:36,390 - core.trader - [31mERROR[0m - Erro ao abrir posição BCHUSD: Erro ao abrir posição: 10027 - AutoTrading disabled by client
Module: trader - Function: _execute_signal - Line: 269
C:\Bancomat\bancomat4\core\trader.py

2025-05-22 23:37:50,716 - core.position_sizing - [31mERROR[0m - Erro no cálculo proporcional: 'AccountInfo' object has no attribute 'get'
Module: position_sizing - Function: _calculate_proportional_volumes - Line: 230
C:\Bancomat\bancomat4\core\position_sizing.py

2025-05-22 23:38:07,652 - core.mt5_connector - [31mERROR[0m - Erro ao fechar posição: 10027 - AutoTrading disabled by client
Module: mt5_connector - Function: close_position - Line: 670
C:\Bancomat\bancomat4\core\mt5_connector.py

2025-05-22 23:38:07,653 - core.trader - [31mERROR[0m - Erro ao fechar BCHUSD: Erro ao fechar posição: 10027 - AutoTrading disabled by client
Module: trader - Function: _close_trade - Line: 430
C:\Bancomat\bancomat4\core\trader.py

2025-05-22 23:38:07,653 - core.mt5_connector - [31mERROR[0m - Erro ao fechar posição: 10027 - AutoTrading disabled by client
Module: mt5_connector - Function: close_position - Line: 670
C:\Bancomat\bancomat4\core\mt5_connector.py

2025-05-22 23:38:07,654 - core.trader - [31mERROR[0m - Erro ao fechar AUDUSD: Erro ao fechar posição: 10027 - AutoTrading disabled by client
Module: trader - Function: _close_trade - Line: 436
C:\Bancomat\bancomat4\core\trader.py

2025-05-23 09:45:26,860 - main - [31mERROR[0m - Erro na interface gráfica: 'AdvancedInterface' object has no attribute 'run'
Module: main - Function: run_gui - Line: 147
C:\Bancomat\bancomat4\main.py

2025-05-23 09:48:02,836 - main - [31mERROR[0m - Erro na interface gráfica: 'AdvancedInterface' object has no attribute 'run'
Module: main - Function: run_gui - Line: 147
C:\Bancomat\bancomat4\main.py

2025-05-23 10:04:27,972 - core.trader - [31mERROR[0m - Erro ao atualizar trade f60c6f70: 'dict' object has no attribute 'point'
Module: trader - Function: _update_active_trades - Line: 416
C:\Bancomat\bancomat4\core\trader.py

2025-05-23 10:04:27,973 - core.trader - [31mERROR[0m - Erro ao atualizar trade a6463ef2: 'dict' object has no attribute 'point'
Module: trader - Function: _update_active_trades - Line: 416
C:\Bancomat\bancomat4\core\trader.py

2025-05-23 10:04:37,977 - core.trader - [31mERROR[0m - Erro ao atualizar trade f60c6f70: 'dict' object has no attribute 'point'
Module: trader - Function: _update_active_trades - Line: 416
C:\Bancomat\bancomat4\core\trader.py

2025-05-23 10:04:37,978 - core.trader - [31mERROR[0m - Erro ao atualizar trade a6463ef2: 'dict' object has no attribute 'point'
Module: trader - Function: _update_active_trades - Line: 416
C:\Bancomat\bancomat4\core\trader.py

2025-05-23 10:04:47,984 - core.trader - [31mERROR[0m - Erro ao atualizar trade f60c6f70: 'dict' object has no attribute 'point'
Module: trader - Function: _update_active_trades - Line: 416
C:\Bancomat\bancomat4\core\trader.py

2025-05-23 10:04:47,985 - core.trader - [31mERROR[0m - Erro ao atualizar trade a6463ef2: 'dict' object has no attribute 'point'
Module: trader - Function: _update_active_trades - Line: 416
C:\Bancomat\bancomat4\core\trader.py

2025-05-23 10:04:57,995 - core.trader - [31mERROR[0m - Erro ao atualizar trade f60c6f70: 'dict' object has no attribute 'point'
Module: trader - Function: _update_active_trades - Line: 416
C:\Bancomat\bancomat4\core\trader.py

2025-05-23 10:04:57,996 - core.trader - [31mERROR[0m - Erro ao atualizar trade a6463ef2: 'dict' object has no attribute 'point'
Module: trader - Function: _update_active_trades - Line: 416
C:\Bancomat\bancomat4\core\trader.py

2025-05-23 10:05:08,005 - core.trader - [31mERROR[0m - Erro ao atualizar trade f60c6f70: 'dict' object has no attribute 'point'
Module: trader - Function: _update_active_trades - Line: 416
C:\Bancomat\bancomat4\core\trader.py

2025-05-23 10:05:08,006 - core.trader - [31mERROR[0m - Erro ao atualizar trade a6463ef2: 'dict' object has no attribute 'point'
Module: trader - Function: _update_active_trades - Line: 416
C:\Bancomat\bancomat4\core\trader.py

2025-05-23 10:05:18,018 - core.trader - [31mERROR[0m - Erro ao atualizar trade f60c6f70: 'dict' object has no attribute 'point'
Module: trader - Function: _update_active_trades - Line: 416
C:\Bancomat\bancomat4\core\trader.py

2025-05-23 10:05:18,019 - core.trader - [31mERROR[0m - Erro ao atualizar trade a6463ef2: 'dict' object has no attribute 'point'
Module: trader - Function: _update_active_trades - Line: 416
C:\Bancomat\bancomat4\core\trader.py

2025-05-23 10:05:28,021 - core.trader - [31mERROR[0m - Erro ao atualizar trade f60c6f70: 'dict' object has no attribute 'point'
Module: trader - Function: _update_active_trades - Line: 416
C:\Bancomat\bancomat4\core\trader.py

2025-05-23 10:05:28,022 - core.trader - [31mERROR[0m - Erro ao atualizar trade a6463ef2: 'dict' object has no attribute 'point'
Module: trader - Function: _update_active_trades - Line: 416
C:\Bancomat\bancomat4\core\trader.py

2025-05-23 10:05:38,036 - core.trader - [31mERROR[0m - Erro ao atualizar trade f60c6f70: 'dict' object has no attribute 'point'
Module: trader - Function: _update_active_trades - Line: 416
C:\Bancomat\bancomat4\core\trader.py

2025-05-23 10:05:38,037 - core.trader - [31mERROR[0m - Erro ao atualizar trade a6463ef2: 'dict' object has no attribute 'point'
Module: trader - Function: _update_active_trades - Line: 416
C:\Bancomat\bancomat4\core\trader.py

2025-05-23 10:05:48,052 - core.trader - [31mERROR[0m - Erro ao atualizar trade f60c6f70: 'dict' object has no attribute 'point'
Module: trader - Function: _update_active_trades - Line: 416
C:\Bancomat\bancomat4\core\trader.py

2025-05-23 10:05:48,053 - core.trader - [31mERROR[0m - Erro ao atualizar trade a6463ef2: 'dict' object has no attribute 'point'
Module: trader - Function: _update_active_trades - Line: 416
C:\Bancomat\bancomat4\core\trader.py

2025-05-23 10:05:58,063 - core.trader - [31mERROR[0m - Erro ao atualizar trade f60c6f70: 'dict' object has no attribute 'point'
Module: trader - Function: _update_active_trades - Line: 416
C:\Bancomat\bancomat4\core\trader.py

2025-05-23 10:05:58,065 - core.trader - [31mERROR[0m - Erro ao atualizar trade a6463ef2: 'dict' object has no attribute 'point'
Module: trader - Function: _update_active_trades - Line: 416
C:\Bancomat\bancomat4\core\trader.py

2025-05-23 10:06:08,073 - core.trader - [31mERROR[0m - Erro ao atualizar trade f60c6f70: 'dict' object has no attribute 'point'
Module: trader - Function: _update_active_trades - Line: 416
C:\Bancomat\bancomat4\core\trader.py

2025-05-23 10:06:08,074 - core.trader - [31mERROR[0m - Erro ao atualizar trade a6463ef2: 'dict' object has no attribute 'point'
Module: trader - Function: _update_active_trades - Line: 416
C:\Bancomat\bancomat4\core\trader.py

2025-05-23 10:06:18,077 - core.trader - [31mERROR[0m - Erro ao atualizar trade f60c6f70: 'dict' object has no attribute 'point'
Module: trader - Function: _update_active_trades - Line: 416
C:\Bancomat\bancomat4\core\trader.py

2025-05-23 10:06:18,078 - core.trader - [31mERROR[0m - Erro ao atualizar trade a6463ef2: 'dict' object has no attribute 'point'
Module: trader - Function: _update_active_trades - Line: 416
C:\Bancomat\bancomat4\core\trader.py

2025-05-23 10:06:28,083 - core.trader - [31mERROR[0m - Erro ao atualizar trade f60c6f70: 'dict' object has no attribute 'point'
Module: trader - Function: _update_active_trades - Line: 416
C:\Bancomat\bancomat4\core\trader.py

2025-05-23 10:06:28,084 - core.trader - [31mERROR[0m - Erro ao atualizar trade a6463ef2: 'dict' object has no attribute 'point'
Module: trader - Function: _update_active_trades - Line: 416
C:\Bancomat\bancomat4\core\trader.py

2025-05-23 10:06:38,098 - core.trader - [31mERROR[0m - Erro ao atualizar trade f60c6f70: 'dict' object has no attribute 'point'
Module: trader - Function: _update_active_trades - Line: 416
C:\Bancomat\bancomat4\core\trader.py

2025-05-23 10:06:38,099 - core.trader - [31mERROR[0m - Erro ao atualizar trade a6463ef2: 'dict' object has no attribute 'point'
Module: trader - Function: _update_active_trades - Line: 416
C:\Bancomat\bancomat4\core\trader.py

2025-05-23 10:06:48,102 - core.trader - [31mERROR[0m - Erro ao atualizar trade f60c6f70: 'dict' object has no attribute 'point'
Module: trader - Function: _update_active_trades - Line: 416
C:\Bancomat\bancomat4\core\trader.py

2025-05-23 10:06:48,103 - core.trader - [31mERROR[0m - Erro ao atualizar trade a6463ef2: 'dict' object has no attribute 'point'
Module: trader - Function: _update_active_trades - Line: 416
C:\Bancomat\bancomat4\core\trader.py

2025-05-23 10:06:58,105 - core.trader - [31mERROR[0m - Erro ao atualizar trade f60c6f70: 'dict' object has no attribute 'point'
Module: trader - Function: _update_active_trades - Line: 416
C:\Bancomat\bancomat4\core\trader.py

2025-05-23 10:06:58,106 - core.trader - [31mERROR[0m - Erro ao atualizar trade a6463ef2: 'dict' object has no attribute 'point'
Module: trader - Function: _update_active_trades - Line: 416
C:\Bancomat\bancomat4\core\trader.py

2025-05-23 10:07:08,107 - core.trader - [31mERROR[0m - Erro ao atualizar trade f60c6f70: 'dict' object has no attribute 'point'
Module: trader - Function: _update_active_trades - Line: 416
C:\Bancomat\bancomat4\core\trader.py

2025-05-23 10:07:08,108 - core.trader - [31mERROR[0m - Erro ao atualizar trade a6463ef2: 'dict' object has no attribute 'point'
Module: trader - Function: _update_active_trades - Line: 416
C:\Bancomat\bancomat4\core\trader.py

2025-05-23 10:07:18,118 - core.trader - [31mERROR[0m - Erro ao atualizar trade f60c6f70: 'dict' object has no attribute 'point'
Module: trader - Function: _update_active_trades - Line: 416
C:\Bancomat\bancomat4\core\trader.py

2025-05-23 10:07:18,119 - core.trader - [31mERROR[0m - Erro ao atualizar trade a6463ef2: 'dict' object has no attribute 'point'
Module: trader - Function: _update_active_trades - Line: 416
C:\Bancomat\bancomat4\core\trader.py

2025-05-23 10:07:28,127 - core.trader - [31mERROR[0m - Erro ao atualizar trade f60c6f70: 'dict' object has no attribute 'point'
Module: trader - Function: _update_active_trades - Line: 416
C:\Bancomat\bancomat4\core\trader.py

2025-05-23 10:07:28,128 - core.trader - [31mERROR[0m - Erro ao atualizar trade a6463ef2: 'dict' object has no attribute 'point'
Module: trader - Function: _update_active_trades - Line: 416
C:\Bancomat\bancomat4\core\trader.py

2025-05-23 10:13:44,729 - ui.advanced_interface - [31mERROR[0m - ❌ Erro ao criar aba de pares: 'AdvancedInterface' object has no attribute 'apply_pairs_filter'
Module: advanced_interface - Function: create_pairs_tab - Line: 490
C:\Bancomat\bancomat4\ui\advanced_interface.py

2025-05-23 10:29:52,280 - core.trader - [31mERROR[0m - Erro ao sincronizar com MT5: 'MT5Connector' object has no attribute 'get_open_positions'
Module: trader - Function: sync_with_mt5_positions - Line: 754
C:\Bancomat\bancomat4\core\trader.py

2025-05-23 10:29:52,280 - core.trader - [31mERROR[0m - Traceback: Traceback (most recent call last):
  File "C:\Bancomat\bancomat4\core\trader.py", line 650, in sync_with_mt5_positions
    positions_info = self.mt5.get_open_positions()
AttributeError: 'MT5Connector' object has no attribute 'get_open_positions'

Module: trader - Function: sync_with_mt5_positions - Line: 756
C:\Bancomat\bancomat4\core\trader.py

2025-05-23 10:59:54,177 - utils.config - [31mERROR[0m - Erro ao carregar configuração: ('signals', None, "Seção obrigatória 'signals' não encontrada")
Module: config - Function: _load_config - Line: 68
C:\Bancomat\bancomat4\utils\config.py

2025-05-23 12:43:17,911 - ui.advanced_interface - [31mERROR[0m - Erro ao atualizar dados de trading: 'AdvancedInterface' object has no attribute 'active_trades'
Module: advanced_interface - Function: update_trading_data - Line: 915
C:\Bancomat\bancomat4\ui\advanced_interface.py

2025-05-23 12:43:18,926 - ui.advanced_interface - [31mERROR[0m - Erro ao atualizar dados de trading: 'AdvancedInterface' object has no attribute 'active_trades'
Module: advanced_interface - Function: update_trading_data - Line: 915
C:\Bancomat\bancomat4\ui\advanced_interface.py

2025-05-23 12:43:19,933 - ui.advanced_interface - [31mERROR[0m - Erro ao atualizar dados de trading: 'AdvancedInterface' object has no attribute 'active_trades'
Module: advanced_interface - Function: update_trading_data - Line: 915
C:\Bancomat\bancomat4\ui\advanced_interface.py

2025-05-23 12:43:20,945 - ui.advanced_interface - [31mERROR[0m - Erro ao atualizar dados de trading: 'AdvancedInterface' object has no attribute 'active_trades'
Module: advanced_interface - Function: update_trading_data - Line: 915
C:\Bancomat\bancomat4\ui\advanced_interface.py

2025-05-23 12:43:21,953 - ui.advanced_interface - [31mERROR[0m - Erro ao atualizar dados de trading: 'AdvancedInterface' object has no attribute 'active_trades'
Module: advanced_interface - Function: update_trading_data - Line: 915
C:\Bancomat\bancomat4\ui\advanced_interface.py

2025-05-23 12:43:22,961 - ui.advanced_interface - [31mERROR[0m - Erro ao atualizar dados de trading: 'AdvancedInterface' object has no attribute 'active_trades'
Module: advanced_interface - Function: update_trading_data - Line: 915
C:\Bancomat\bancomat4\ui\advanced_interface.py

2025-05-23 12:43:23,972 - ui.advanced_interface - [31mERROR[0m - Erro ao atualizar dados de trading: 'AdvancedInterface' object has no attribute 'active_trades'
Module: advanced_interface - Function: update_trading_data - Line: 915
C:\Bancomat\bancomat4\ui\advanced_interface.py

2025-05-23 12:43:24,983 - ui.advanced_interface - [31mERROR[0m - Erro ao atualizar dados de trading: 'AdvancedInterface' object has no attribute 'active_trades'
Module: advanced_interface - Function: update_trading_data - Line: 915
C:\Bancomat\bancomat4\ui\advanced_interface.py

2025-05-23 12:43:25,993 - ui.advanced_interface - [31mERROR[0m - Erro ao atualizar dados de trading: 'AdvancedInterface' object has no attribute 'active_trades'
Module: advanced_interface - Function: update_trading_data - Line: 915
C:\Bancomat\bancomat4\ui\advanced_interface.py

2025-05-23 12:43:27,001 - ui.advanced_interface - [31mERROR[0m - Erro ao atualizar dados de trading: 'AdvancedInterface' object has no attribute 'active_trades'
Module: advanced_interface - Function: update_trading_data - Line: 915
C:\Bancomat\bancomat4\ui\advanced_interface.py

2025-05-23 12:43:28,010 - ui.advanced_interface - [31mERROR[0m - Erro ao atualizar dados de trading: 'AdvancedInterface' object has no attribute 'active_trades'
Module: advanced_interface - Function: update_trading_data - Line: 915
C:\Bancomat\bancomat4\ui\advanced_interface.py

2025-05-23 12:54:40,581 - ui.advanced_interface - [31mERROR[0m - Erro ao atualizar dados de trading: 'total_profit'
Module: advanced_interface - Function: update_trading_data - Line: 928
C:\Bancomat\bancomat4\ui\advanced_interface.py

2025-05-23 12:54:41,592 - ui.advanced_interface - [31mERROR[0m - Erro ao atualizar dados de trading: 'total_profit'
Module: advanced_interface - Function: update_trading_data - Line: 928
C:\Bancomat\bancomat4\ui\advanced_interface.py

2025-05-23 12:54:42,602 - ui.advanced_interface - [31mERROR[0m - Erro ao atualizar dados de trading: 'total_profit'
Module: advanced_interface - Function: update_trading_data - Line: 928
C:\Bancomat\bancomat4\ui\advanced_interface.py

2025-05-23 12:54:43,613 - ui.advanced_interface - [31mERROR[0m - Erro ao atualizar dados de trading: 'total_profit'
Module: advanced_interface - Function: update_trading_data - Line: 928
C:\Bancomat\bancomat4\ui\advanced_interface.py

2025-05-23 12:54:44,622 - ui.advanced_interface - [31mERROR[0m - Erro ao atualizar dados de trading: 'total_profit'
Module: advanced_interface - Function: update_trading_data - Line: 928
C:\Bancomat\bancomat4\ui\advanced_interface.py

2025-05-23 12:54:45,632 - ui.advanced_interface - [31mERROR[0m - Erro ao atualizar dados de trading: 'total_profit'
Module: advanced_interface - Function: update_trading_data - Line: 928
C:\Bancomat\bancomat4\ui\advanced_interface.py

2025-05-24 11:28:51,516 - ui.advanced_interface - [31mERROR[0m - Erro ao gerar relatório: name 'Path' is not defined
Module: advanced_interface - Function: generate_report - Line: 1593
C:\Bancomat\bancomat4\ui\advanced_interface.py

